# ShopCI - SaaS E-Commerce Platform

A comprehensive SaaS e-commerce platform similar to Shopify, built with CodeIgniter 4, Bootstrap, jQuery, and AI-powered features.

## 🚀 Features

- **Multi-tenant SaaS Architecture**: Each shop gets its own database
- **Drag-and-Drop Storefront Builder**: 16 different section types with 7 design variants each
- **AI-Powered Enhancements**: Design assistance, smart tagging, dynamic pricing, and more
- **Role-Based Access Control**: Platform Admin, Merchant Admin, Merchant User, Customer
- **Payment Gateway Integration**: Stripe, PayPal, and more
- **Marketing Tools**: Email campaigns, SEO optimization, social media integration
- **Analytics & Reporting**: Sales reports, customer insights, predictive analytics
- **Multi-language & Currency Support**: Localization and geotargeting

## 🛠 Technology Stack

- **Backend**: CodeIgniter 4 (PHP)
- **Frontend**: Bootstrap 5 + jQuery
- **Database**: MySQL (multi-tenant)
- **AI Server**: Python + TensorFlow/FastAPI
- **Hosting**: Linux LAMP stack

## 📁 Project Structure

```
shopci.test/
├── backend/                 # CodeIgniter 4 application
│   ├── app/                # Application code
│   ├── public/             # Web root
│   └── ...
├── frontend/               # Frontend assets and templates
│   ├── assets/            # CSS, JS, Images
│   └── templates/         # HTML templates
├── ai-server/             # Python AI microservice
├── database/              # Database schemas and migrations
├── docs/                  # Documentation
├── requirements.md        # Project requirements
├── sections.md           # Storefront section specifications
└── unittests.md          # Testing specifications
```

## 🚦 Getting Started

### Prerequisites

- PHP 8.1+
- Composer
- MySQL 8.0+
- Python 3.9+
- Node.js (for frontend build tools)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd shopci.test
   ```

2. **Backend Setup**
   ```bash
   cd backend
   composer install
   cp env .env
   # Configure database settings in .env
   php spark migrate
   php spark serve
   ```

3. **AI Server Setup**
   ```bash
   cd ai-server
   pip install -r requirements.txt
   python main.py
   ```

4. **Frontend Setup**
   ```bash
   cd frontend
   # Frontend assets are served through CodeIgniter
   ```

## 🏗 Development Phases

- [x] **Phase 1**: Project Structure Setup
- [ ] **Phase 2**: User Authentication & Authorization
- [ ] **Phase 3**: Storefront Builder
- [ ] **Phase 4**: Product Management
- [ ] **Phase 5**: Payment Gateway Integration
- [ ] **Phase 6**: Marketing & SEO Tools
- [ ] **Phase 7**: Analytics & Reporting
- [ ] **Phase 8**: Customer Management
- [ ] **Phase 9**: Shipping & Fulfillment
- [ ] **Phase 10**: Security & Compliance
- [ ] **Phase 11**: Multi-language & Currency Support
- [ ] **Phase 12**: Testing & Deployment

## 🧪 Testing

Run the test suite:
```bash
cd backend
php spark test
```

## 📚 Documentation

- [Requirements](requirements.md)
- [Storefront Sections](sections.md)
- [Unit Tests](unittests.md)
- [API Documentation](docs/api.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support, email <EMAIL> or join our Slack channel.
