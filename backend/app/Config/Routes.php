<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */

// Default route
$routes->get('/', 'Home::index');

// Health check routes
$routes->get('health', 'Health::index');
$routes->get('ping', 'Health::ping');

// API Routes
$routes->group('api', ['namespace' => 'App\Controllers'], function($routes) {

    // Health check
    $routes->get('health', 'Health::index');
    $routes->get('ping', 'Health::ping');

    // Authentication routes
    $routes->group('auth', function($routes) {
        $routes->post('register', 'Auth::register');
        $routes->post('login', 'Auth::login');
        $routes->post('logout', 'Auth::logout');
        $routes->post('refresh', 'Auth::refresh');
        $routes->get('me', 'Auth::me');
    });

    // Platform admin routes
    $routes->group('admin', ['filter' => 'auth:admin'], function($routes) {
        $routes->get('users', 'Admin::getUsers');
        $routes->get('shops', 'Admin::getShops');
        $routes->get('analytics', 'Admin::getAnalytics');
    });

    // Merchant routes
    $routes->group('merchant', ['filter' => 'auth:merchant'], function($routes) {
        $routes->get('dashboard', 'Merchant::dashboard');
        $routes->resource('shops', ['controller' => 'Shops']);
    });

    // Shop management routes (tenant-specific)
    $routes->group('shop/(:segment)', function($routes) {
        $routes->resource('products', ['controller' => 'Products']);
        $routes->resource('categories', ['controller' => 'Categories']);
        $routes->resource('orders', ['controller' => 'Orders']);
        $routes->resource('customers', ['controller' => 'Customers']);
    });

    // AI service routes
    $routes->group('ai', function($routes) {
        $routes->post('design/suggestions', 'AI::designSuggestions');
        $routes->post('products/tagging', 'AI::productTagging');
        $routes->post('products/pricing', 'AI::pricingAnalysis');
        $routes->post('marketing/seo', 'AI::seoKeywords');
        $routes->post('analytics/predictions', 'AI::predictions');
        $routes->post('chatbot/response', 'AI::chatbotResponse');
    });
});

// Frontend routes (for serving the admin panel and storefront builder)
$routes->group('admin', function($routes) {
    $routes->get('/', 'Frontend::adminDashboard');
    $routes->get('login', 'Frontend::adminLogin');
    $routes->get('register', 'Frontend::adminRegister');
    $routes->get('shops', 'Frontend::shops');
    $routes->get('shops/(:segment)', 'Frontend::shopDetails/$1');
});

// Storefront routes (dynamic based on shop domain)
$routes->group('storefront/(:segment)', function($routes) {
    $routes->get('/', 'Storefront::index/$1');
    $routes->get('products', 'Storefront::products/$1');
    $routes->get('products/(:segment)', 'Storefront::productDetails/$1/$2');
    $routes->get('categories/(:segment)', 'Storefront::category/$1/$2');
    $routes->post('cart/add', 'Storefront::addToCart/$1');
    $routes->get('checkout', 'Storefront::checkout/$1');
    $routes->post('checkout/process', 'Storefront::processCheckout/$1');
});
