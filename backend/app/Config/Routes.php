<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */

// Default route
$routes->get('/', 'Home::index');

// Health check routes
$routes->get('health', 'Health::index');
$routes->get('ping', 'Health::ping');

// Email test route
$routes->get('test-email', 'Email::test');

// API Routes
$routes->group('api', ['namespace' => 'App\Controllers'], function($routes) {

    // Health check
    $routes->get('health', 'Health::index');
    $routes->get('ping', 'Health::ping');

    // Authentication routes
    $routes->group('auth', function($routes) {
        $routes->post('register', 'Auth::register');
        $routes->post('login', 'Auth::login');
        $routes->post('logout', 'Auth::logout');
        $routes->post('refresh', 'Auth::refresh');
        $routes->get('me', 'Auth::me');
        $routes->post('forgot-password', 'Auth::forgotPassword');
        $routes->post('reset-password', 'Auth::resetPassword');
        $routes->get('test-db', 'Auth::testDb');
    });

    // Shop management routes
    $routes->group('shops', function($routes) {
        $routes->get('/', 'Shop::index');
        $routes->get('my', 'Shop::myShops');
        $routes->get('themes', 'Shop::themes');
        $routes->get('(:num)', 'Shop::show/$1');
        $routes->post('/', 'Shop::create');
        $routes->put('(:num)', 'Shop::update/$1');
        $routes->delete('(:num)', 'Shop::delete/$1');
    });

    // Builder routes
    $routes->group('builder', function($routes) {
        $routes->get('components', 'Builder::components');
        $routes->get('(:num)/page/(:segment)', 'Builder::getPage/$1/$2');
        $routes->get('(:num)/page', 'Builder::getPage/$1');
        $routes->post('(:num)/page', 'Builder::savePage/$1');
        $routes->get('(:num)/preview', 'Builder::preview/$1');
        $routes->post('(:num)/publish', 'Builder::publish/$1');
    });

    // Product management routes
    $routes->group('products', function($routes) {
        $routes->get('/', 'Product::index');
        $routes->get('shop/(:num)', 'Product::index/$1');
        $routes->get('(:num)', 'Product::show/$1');
        $routes->post('/', 'Product::create');
        $routes->put('(:num)', 'Product::update/$1');
        $routes->delete('(:num)', 'Product::delete/$1');
        $routes->get('shop/(:num)/search', 'Product::search/$1');
        $routes->get('shop/(:num)/featured', 'Product::featured/$1');
        $routes->post('ai/generate-description', 'Product::generateDescription');
    });

    // Payment and order routes
    $routes->group('payments', function($routes) {
        $routes->post('create-intent', 'Payment::createIntent');
        $routes->post('process', 'Payment::processPayment');
        $routes->get('methods/(:num)', 'Payment::getPaymentMethods/$1');
        $routes->post('webhook/(:segment)', 'Payment::webhook/$1');
        $routes->post('refund/(:num)', 'Payment::refund/$1');
    });

    // Platform admin routes
    $routes->group('admin', ['filter' => 'auth:admin'], function($routes) {
        $routes->get('users', 'Admin::getUsers');
        $routes->get('shops', 'Admin::getShops');
        $routes->get('analytics', 'Admin::getAnalytics');
    });

    // Merchant routes
    $routes->group('merchant', ['filter' => 'auth:merchant'], function($routes) {
        $routes->get('dashboard', 'Merchant::dashboard');
        $routes->resource('shops', ['controller' => 'Shops']);
    });

    // Shop management routes (tenant-specific)
    $routes->group('shop/(:segment)', function($routes) {
        $routes->resource('products', ['controller' => 'Products']);
        $routes->resource('categories', ['controller' => 'Categories']);
        $routes->resource('orders', ['controller' => 'Orders']);
        $routes->resource('customers', ['controller' => 'Customers']);
    });

    // AI service routes
    $routes->group('ai', function($routes) {
        $routes->post('design/suggestions', 'AI::designSuggestions');
        $routes->post('products/tagging', 'AI::productTagging');
        $routes->post('products/pricing', 'AI::pricingAnalysis');
        $routes->post('marketing/seo', 'AI::seoKeywords');
        $routes->post('analytics/predictions', 'AI::predictions');
        $routes->post('chatbot/response', 'AI::chatbotResponse');
    });
});

// Frontend routes (for serving the admin panel and storefront builder)
$routes->group('admin', function($routes) {
    $routes->get('/', 'Frontend::adminDashboard');
    $routes->get('login', 'Frontend::adminLogin');
    $routes->get('register', 'Frontend::adminRegister');
    $routes->get('forgot-password', 'Frontend::adminForgotPassword');
    $routes->get('reset-password', 'Frontend::adminResetPassword');
    $routes->get('shops', 'Frontend::shops');
    $routes->get('builder/(:num)', 'Frontend::builder/$1');
    $routes->get('shops/(:segment)', 'Frontend::shopDetails/$1');
});

// Storefront routes (dynamic based on shop domain)
$routes->group('storefront/(:segment)', function($routes) {
    $routes->get('/', 'Storefront::index/$1');
    $routes->get('products', 'Storefront::products/$1');
    $routes->get('products/(:segment)', 'Storefront::productDetails/$1/$2');
    $routes->get('categories/(:segment)', 'Storefront::category/$1/$2');
    $routes->post('cart/add', 'Storefront::addToCart/$1');
    $routes->get('checkout', 'Storefront::checkout/$1');
    $routes->post('checkout/process', 'Storefront::processCheckout/$1');
});
