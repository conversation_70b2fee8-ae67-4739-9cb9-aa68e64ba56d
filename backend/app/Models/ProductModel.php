<?php

namespace App\Models;

use CodeIgniter\Model;

class ProductModel extends Model
{
    protected $table            = 'products';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'uuid', 'shop_id', 'name', 'description', 'short_description',
        'price', 'sale_price', 'sku', 'stock_quantity', 'manage_stock',
        'category_id', 'tags', 'images', 'featured_image', 'status',
        'weight', 'dimensions', 'seo_title', 'seo_description', 'seo_keywords',
        'featured',
        // Legacy fields
        'slug', 'compare_price', 'cost_price', 'track_inventory',
        'inventory_quantity', 'low_stock_threshold', 'is_digital',
        'requires_shipping', 'is_active', 'is_featured', 'meta_title', 'meta_description'
    ];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [
        'stock_quantity' => 'integer',
        'manage_stock' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules = [
        'shop_id' => 'required|integer',
        'name' => 'required|min_length[2]|max_length[255]',
        'price' => 'required|decimal|greater_than[0]',
        'sku' => 'permit_empty|max_length[100]',
        'status' => 'permit_empty|in_list[active,inactive,draft]'
    ];

    protected $validationMessages = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['generateUuid'];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Generate UUID before insert
     */
    protected function generateUuid(array $data)
    {
        if (!isset($data['data']['uuid'])) {
            $data['data']['uuid'] = $this->createUUID();
        }
        return $data;
    }

    /**
     * Generate UUID v4
     */
    private function createUUID(): string
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

    /**
     * Get products with category information
     */
    public function getProductsWithCategory($shopId = null)
    {
        $builder = $this->select('products.*, categories.name as category_name')
                        ->join('categories', 'categories.id = products.category_id', 'left');

        if ($shopId) {
            $builder->where('products.shop_id', $shopId);
        }

        return $builder->orderBy('products.created_at', 'DESC')->findAll();
    }

    /**
     * Get featured products
     */
    public function getFeaturedProducts($shopId, $limit = 8)
    {
        return $this->where('shop_id', $shopId)
                    ->where('status', 'active')
                    ->where('featured', 1)
                    ->orderBy('created_at', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }

    /**
     * Search products
     */
    public function searchProducts($shopId, $query, $categoryId = null)
    {
        $builder = $this->where('shop_id', $shopId)
                        ->where('status', 'active')
                        ->groupStart()
                            ->like('name', $query)
                            ->orLike('description', $query)
                            ->orLike('tags', $query)
                        ->groupEnd();

        if ($categoryId) {
            $builder->where('category_id', $categoryId);
        }

        return $builder->orderBy('name', 'ASC')->findAll();
    }

    /**
     * Update stock quantity
     */
    public function updateStock($productId, $quantity, $operation = 'decrease')
    {
        $product = $this->find($productId);
        if (!$product || !$product['manage_stock']) {
            return false;
        }

        $newQuantity = $operation === 'decrease'
            ? $product['stock_quantity'] - $quantity
            : $product['stock_quantity'] + $quantity;

        return $this->update($productId, ['stock_quantity' => max(0, $newQuantity)]);
    }
}
