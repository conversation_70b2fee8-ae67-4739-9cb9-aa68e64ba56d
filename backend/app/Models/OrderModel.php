<?php

namespace App\Models;

use CodeIgniter\Model;

class OrderModel extends Model
{
    protected $table            = 'orders';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'uuid', 'shop_id', 'customer_id', 'order_number', 'status',
        'subtotal', 'tax_amount', 'shipping_amount', 'discount_amount', 'total_amount',
        'currency', 'payment_status', 'payment_method', 'payment_gateway',
        'billing_address', 'shipping_address', 'notes', 'metadata'
    ];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [
        'subtotal' => 'float',
        'tax_amount' => 'float',
        'shipping_amount' => 'float',
        'discount_amount' => 'float',
        'total_amount' => 'float',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules = [
        'shop_id' => 'required|integer',
        'total_amount' => 'required|decimal|greater_than[0]',
        'currency' => 'required|max_length[3]',
        'status' => 'permit_empty|in_list[pending,processing,shipped,delivered,cancelled,refunded]',
        'payment_status' => 'permit_empty|in_list[pending,paid,failed,refunded,partially_refunded]'
    ];

    protected $validationMessages = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['generateUuid', 'generateOrderNumber'];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Generate UUID before insert
     */
    protected function generateUuid(array $data)
    {
        if (!isset($data['data']['uuid'])) {
            $data['data']['uuid'] = $this->createUUID();
        }
        return $data;
    }

    /**
     * Generate order number before insert
     */
    protected function generateOrderNumber(array $data)
    {
        if (!isset($data['data']['order_number'])) {
            $data['data']['order_number'] = 'ORD-' . date('Y') . '-' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
        }
        return $data;
    }

    /**
     * Generate UUID v4
     */
    private function createUUID(): string
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

    /**
     * Get orders with customer information
     */
    public function getOrdersWithCustomer($shopId = null)
    {
        $builder = $this->select('orders.*, customers.first_name, customers.last_name, customers.email')
                        ->join('customers', 'customers.id = orders.customer_id', 'left');

        if ($shopId) {
            $builder->where('orders.shop_id', $shopId);
        }

        return $builder->orderBy('orders.created_at', 'DESC')->findAll();
    }

    /**
     * Get order statistics
     */
    public function getOrderStats($shopId, $period = '30_days')
    {
        $dateCondition = '';
        switch ($period) {
            case '7_days':
                $dateCondition = 'orders.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
                break;
            case '30_days':
                $dateCondition = 'orders.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
                break;
            case '90_days':
                $dateCondition = 'orders.created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)';
                break;
            case 'year':
                $dateCondition = 'orders.created_at >= DATE_SUB(NOW(), INTERVAL 1 YEAR)';
                break;
            default:
                $dateCondition = '1=1';
        }

        $stats = $this->select('
                COUNT(*) as total_orders,
                SUM(total_amount) as total_revenue,
                AVG(total_amount) as average_order_value,
                COUNT(CASE WHEN status = "delivered" THEN 1 END) as completed_orders,
                COUNT(CASE WHEN status = "cancelled" THEN 1 END) as cancelled_orders
            ')
            ->where('shop_id', $shopId)
            ->where($dateCondition)
            ->first();

        return $stats;
    }

    /**
     * Update order status
     */
    public function updateOrderStatus($orderId, $status, $notes = null)
    {
        $data = ['status' => $status];
        if ($notes) {
            $data['notes'] = $notes;
        }

        return $this->update($orderId, $data);
    }
}
