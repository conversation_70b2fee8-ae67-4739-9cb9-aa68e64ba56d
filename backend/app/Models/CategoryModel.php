<?php

namespace App\Models;

use CodeIgniter\Model;

class CategoryModel extends Model
{
    protected $table            = 'categories';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'uuid', 'shop_id', 'name', 'description', 'slug',
        'parent_id', 'image_url', 'is_active', 'sort_order',
        'meta_title', 'meta_description'
    ];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [
        'parent_id' => '?integer',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules = [
        'shop_id' => 'required|integer',
        'name' => 'required|min_length[2]|max_length[255]',
        'slug' => 'permit_empty|max_length[255]',
        'is_active' => 'permit_empty|in_list[0,1]'
    ];

    protected $validationMessages = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['generateUuid', 'generateSlug'];
    protected $afterInsert    = [];
    protected $beforeUpdate   = ['generateSlug'];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Generate UUID before insert
     */
    protected function generateUuid(array $data)
    {
        if (!isset($data['data']['uuid'])) {
            $data['data']['uuid'] = $this->createUUID();
        }
        return $data;
    }

    /**
     * Generate slug before insert/update
     */
    protected function generateSlug(array $data)
    {
        if (isset($data['data']['name']) && empty($data['data']['slug'])) {
            $data['data']['slug'] = url_title($data['data']['name'], '-', true);
        }
        return $data;
    }

    /**
     * Generate UUID v4
     */
    private function createUUID(): string
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

    /**
     * Get categories for a shop
     */
    public function getShopCategories($shopId)
    {
        return $this->where('shop_id', $shopId)
                    ->where('is_active', 1)
                    ->orderBy('sort_order', 'ASC')
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }

    /**
     * Get category tree (hierarchical)
     */
    public function getCategoryTree($shopId, $parentId = null)
    {
        $categories = $this->where('shop_id', $shopId)
                           ->where('parent_id', $parentId)
                           ->where('is_active', 1)
                           ->orderBy('sort_order', 'ASC')
                           ->findAll();

        foreach ($categories as &$category) {
            $category['children'] = $this->getCategoryTree($shopId, $category['id']);
        }

        return $categories;
    }

    /**
     * Get category with product count
     */
    public function getCategoriesWithProductCount($shopId)
    {
        return $this->select('categories.*, COUNT(products.id) as product_count')
                    ->join('products', 'products.category_id = categories.id', 'left')
                    ->where('categories.shop_id', $shopId)
                    ->where('categories.is_active', 1)
                    ->groupBy('categories.id')
                    ->orderBy('categories.sort_order', 'ASC')
                    ->findAll();
    }
}
