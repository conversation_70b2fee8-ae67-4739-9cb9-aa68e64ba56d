<?php

namespace App\Models;

use CodeIgniter\Model;

class ShopModel extends Model
{
    protected $table            = 'shops';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'uuid', 'name', 'description', 'subdomain', 'custom_domain',
        'theme', 'owner_id', 'subscription_plan_id', 'status',
        'settings', 'seo_title', 'seo_description', 'seo_keywords',
        // Legacy fields
        'shop_name', 'shop_domain', 'database_name', 'plan_id', 'trial_ends_at'
    ];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules = [
        'name' => 'required|min_length[2]|max_length[100]',
        'subdomain' => 'required|min_length[3]|max_length[50]|alpha_dash|is_unique[shops.subdomain,id,{id}]',
        'owner_id' => 'required|integer',
        'theme' => 'permit_empty|in_list[default,modern,classic,minimal]',
        'status' => 'permit_empty|in_list[active,inactive,suspended]'
    ];

    protected $validationMessages = [
        'subdomain' => [
            'is_unique' => 'This subdomain is already taken. Please choose another one.'
        ]
    ];

    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['generateUuid'];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Generate UUID before insert
     */
    protected function generateUuid(array $data)
    {
        if (!isset($data['data']['uuid'])) {
            $data['data']['uuid'] = $this->createUUID();
        }
        return $data;
    }

    /**
     * Generate UUID v4
     */
    private function createUUID(): string
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

    /**
     * Get shops with owner information
     */
    public function getShopsWithOwners()
    {
        return $this->select('shops.*, platform_users.first_name, platform_users.last_name, platform_users.email')
                    ->join('platform_users', 'platform_users.id = shops.owner_id')
                    ->orderBy('shops.created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get shop with detailed information
     */
    public function getShopWithDetails($id)
    {
        return $this->select('shops.*, platform_users.first_name, platform_users.last_name, platform_users.email')
                    ->join('platform_users', 'platform_users.id = shops.owner_id')
                    ->where('shops.id', $id)
                    ->first();
    }

    /**
     * Get shop by subdomain
     */
    public function getBySubdomain($subdomain)
    {
        return $this->where('subdomain', $subdomain)
                    ->where('status', 'active')
                    ->first();
    }

    /**
     * Get shop by custom domain
     */
    public function getByDomain($domain)
    {
        return $this->where('custom_domain', $domain)
                    ->where('status', 'active')
                    ->first();
    }

    /**
     * Get user's shops
     */
    public function getUserShops($userId)
    {
        return $this->where('owner_id', $userId)
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Update shop settings
     */
    public function updateSettings($shopId, $settings)
    {
        $shop = $this->find($shopId);
        if (!$shop) {
            return false;
        }

        $currentSettings = $shop['settings'] ?? [];
        $newSettings = array_merge($currentSettings, $settings);

        return $this->update($shopId, ['settings' => $newSettings]);
    }
}
