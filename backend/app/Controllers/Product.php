<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\ProductModel;
use App\Models\CategoryModel;

class Product extends BaseController
{
    protected $productModel;
    protected $categoryModel;

    public function __construct()
    {
        $this->productModel = new ProductModel();
        $this->categoryModel = new CategoryModel();
    }

    /**
     * Get all products for a shop
     */
    public function index($shopId = null)
    {
        try {
            if ($shopId) {
                $products = $this->productModel->getProductsWithCategory($shopId);
            } else {
                $products = $this->productModel->getProductsWithCategory();
            }

            return $this->response->setJSON([
                'success' => true,
                'data' => $products
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch products'
            ])->setStatusCode(500);
        }
    }

    /**
     * Get single product
     */
    public function show($id)
    {
        try {
            $product = $this->productModel->find($id);

            if (!$product) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Product not found'
                ])->setStatusCode(404);
            }

            return $this->response->setJSON([
                'success' => true,
                'data' => $product
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch product'
            ])->setStatusCode(500);
        }
    }

    /**
     * Create new product
     */
    public function create()
    {
        if ($this->request->getMethod() !== 'POST') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ])->setStatusCode(405);
        }

        $json = $this->request->getJSON();
        $name = $json->name ?? '';
        $data = [
            'shop_id' => $json->shop_id ?? 1, // TODO: Get from JWT token
            'name' => $name,
            'description' => $json->description ?? '',
            'short_description' => $json->short_description ?? '',
            'price' => $json->price ?? 0,
            'sale_price' => $json->sale_price ?? null,
            'sku' => $json->sku ?? '',
            'stock_quantity' => $json->stock_quantity ?? 0,
            'manage_stock' => $json->manage_stock ?? true,
            'category_id' => $json->category_id ?? null,
            'tags' => $json->tags ?? '',
            'featured_image' => $json->featured_image ?? '',
            'images' => $json->images ?? '',
            'status' => $json->status ?? 'active',
            'weight' => $json->weight ?? null,
            'dimensions' => $json->dimensions ?? '{}',
            'seo_title' => $json->seo_title ?? '',
            'seo_description' => $json->seo_description ?? '',
            'seo_keywords' => $json->seo_keywords ?? '',
            'featured' => $json->featured ?? false,
            // Legacy fields for compatibility
            'slug' => url_title($name, '-', true),
            'track_inventory' => $json->manage_stock ?? true,
            'inventory_quantity' => $json->stock_quantity ?? 0,
            'is_active' => $json->status === 'active' ? 1 : 0,
            'is_featured' => $json->featured ?? false,
            'meta_title' => $json->seo_title ?? '',
            'meta_description' => $json->seo_description ?? ''
        ];

        $rules = [
            'shop_id' => 'required|integer',
            'name' => 'required|min_length[2]|max_length[255]',
            'price' => 'required|decimal|greater_than[0]',
            'sku' => 'permit_empty|max_length[100]',
            'status' => 'permit_empty|in_list[active,inactive,draft]'
        ];

        if (!$this->validate($rules, $data)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $this->validator->getErrors()
            ])->setStatusCode(400);
        }

        try {
            $productId = $this->productModel->insert($data);

            if ($productId) {
                $product = $this->productModel->find($productId);

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Product created successfully',
                    'data' => $product
                ])->setStatusCode(201);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to create product',
                    'errors' => $this->productModel->errors()
                ])->setStatusCode(500);
            }
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to create product: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Update product
     */
    public function update($id)
    {
        if ($this->request->getMethod() !== 'PUT' && $this->request->getMethod() !== 'POST') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ])->setStatusCode(405);
        }

        $product = $this->productModel->find($id);
        if (!$product) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Product not found'
            ])->setStatusCode(404);
        }

        $json = $this->request->getJSON();
        $data = [
            'name' => $json->name ?? $product['name'],
            'description' => $json->description ?? $product['description'],
            'short_description' => $json->short_description ?? $product['short_description'],
            'price' => $json->price ?? $product['price'],
            'sale_price' => $json->sale_price ?? $product['sale_price'],
            'sku' => $json->sku ?? $product['sku'],
            'stock_quantity' => $json->stock_quantity ?? $product['stock_quantity'],
            'manage_stock' => $json->manage_stock ?? $product['manage_stock'],
            'category_id' => $json->category_id ?? $product['category_id'],
            'tags' => $json->tags ?? $product['tags'],
            'featured_image' => $json->featured_image ?? $product['featured_image'],
            'images' => $json->images ?? $product['images'],
            'status' => $json->status ?? $product['status'],
            'weight' => $json->weight ?? $product['weight'],
            'dimensions' => $json->dimensions ?? $product['dimensions'],
            'seo_title' => $json->seo_title ?? $product['seo_title'],
            'seo_description' => $json->seo_description ?? $product['seo_description'],
            'seo_keywords' => $json->seo_keywords ?? $product['seo_keywords']
        ];

        try {
            $this->productModel->update($id, $data);
            $updatedProduct = $this->productModel->find($id);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Product updated successfully',
                'data' => $updatedProduct
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to update product'
            ])->setStatusCode(500);
        }
    }

    /**
     * Delete product
     */
    public function delete($id)
    {
        if ($this->request->getMethod() !== 'DELETE') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ])->setStatusCode(405);
        }

        $product = $this->productModel->find($id);
        if (!$product) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Product not found'
            ])->setStatusCode(404);
        }

        try {
            $this->productModel->delete($id);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Product deleted successfully'
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to delete product'
            ])->setStatusCode(500);
        }
    }

    /**
     * Generate AI product description
     */
    public function generateDescription()
    {
        if ($this->request->getMethod() !== 'POST') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ])->setStatusCode(405);
        }

        $json = $this->request->getJSON();
        $productName = $json->product_name ?? '';
        $category = $json->category ?? '';
        $features = $json->features ?? [];

        if (empty($productName)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Product name is required'
            ])->setStatusCode(400);
        }

        try {
            // Call AI service to generate description
            $aiDescription = $this->callAIService('generate-description', [
                'product_name' => $productName,
                'category' => $category,
                'features' => $features
            ]);

            return $this->response->setJSON([
                'success' => true,
                'data' => [
                    'description' => $aiDescription['description'],
                    'short_description' => $aiDescription['short_description'],
                    'seo_title' => $aiDescription['seo_title'],
                    'seo_description' => $aiDescription['seo_description'],
                    'seo_keywords' => $aiDescription['seo_keywords']
                ]
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to generate AI description'
            ])->setStatusCode(500);
        }
    }

    /**
     * Search products
     */
    public function search($shopId)
    {
        $query = $this->request->getGet('q');
        $categoryId = $this->request->getGet('category');

        if (empty($query)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Search query is required'
            ])->setStatusCode(400);
        }

        try {
            $products = $this->productModel->searchProducts($shopId, $query, $categoryId);

            return $this->response->setJSON([
                'success' => true,
                'data' => $products
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to search products'
            ])->setStatusCode(500);
        }
    }

    /**
     * Get featured products
     */
    public function featured($shopId)
    {
        $limit = $this->request->getGet('limit') ?? 8;

        try {
            $products = $this->productModel->getFeaturedProducts($shopId, $limit);

            return $this->response->setJSON([
                'success' => true,
                'data' => $products
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch featured products'
            ])->setStatusCode(500);
        }
    }

    /**
     * Call AI service for various tasks
     */
    private function callAIService($endpoint, $data)
    {
        $aiBaseUrl = 'http://ai:8000'; // AI service URL

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $aiBaseUrl . '/' . $endpoint);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Accept: application/json'
            ]);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode !== 200) {
                throw new \Exception('AI service returned error: ' . $httpCode);
            }

            $result = json_decode($response, true);

            if (!$result || !isset($result['success']) || !$result['success']) {
                throw new \Exception('AI service returned invalid response');
            }

            return $result['data'];
        } catch (\Exception $e) {
            // Fallback to mock data if AI service is unavailable
            return $this->getMockAIResponse($endpoint, $data);
        }
    }

    /**
     * Mock AI responses for development
     */
    private function getMockAIResponse($endpoint, $data)
    {
        switch ($endpoint) {
            case 'generate-description':
                $productName = $data['product_name'];
                $category = $data['category'] ?? 'product';

                return [
                    'description' => "Discover the amazing {$productName}, a premium {$category} designed to exceed your expectations. Crafted with attention to detail and built to last, this exceptional item combines functionality with style. Whether you're looking for quality, durability, or performance, the {$productName} delivers on all fronts. Perfect for both everyday use and special occasions, it's an investment in excellence that you'll appreciate for years to come.",
                    'short_description' => "Premium {$productName} - exceptional quality and performance in a {$category} that exceeds expectations.",
                    'seo_title' => "{$productName} - Premium {$category} | High Quality & Durable",
                    'seo_description' => "Shop the best {$productName} - premium {$category} with exceptional quality, durability and performance. Free shipping available.",
                    'seo_keywords' => "{$productName}, {$category}, premium, quality, durable, buy online"
                ];

            default:
                return ['message' => 'Mock AI response'];
        }
    }
}
