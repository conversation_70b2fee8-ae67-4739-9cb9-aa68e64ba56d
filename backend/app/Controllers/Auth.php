<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\PlatformUser;
use App\Controllers\Email;
use Firebase\JWT\JWT;

class Auth extends BaseController
{
    protected $userModel;
    protected $jwtSecret;
    protected $jwtAlgorithm;

    public function __construct()
    {
        $this->userModel = new PlatformUser();
        $this->jwtSecret = getenv('jwt.secret') ?: 'your-default-secret-key';
        $this->jwtAlgorithm = getenv('jwt.algorithm') ?: 'HS256';
    }

    /**
     * User Registration
     */
    public function register()
    {
        if ($this->request->getMethod() !== 'POST') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ])->setStatusCode(405);
        }

        // Get JSON input
        $json = $this->request->getJSON();
        $email = $json->email ?? $this->request->getPost('email');
        $password = $json->password ?? $this->request->getPost('password');
        $firstName = $json->first_name ?? $this->request->getPost('first_name');
        $lastName = $json->last_name ?? $this->request->getPost('last_name');
        $role = $json->role ?? $this->request->getPost('role') ?? 'merchant';

        $rules = [
            'email' => 'required|valid_email|is_unique[platform_users.email]',
            'password' => 'required|min_length[6]',
            'first_name' => 'required|min_length[2]|max_length[100]',
            'last_name' => 'required|min_length[2]|max_length[100]',
            'role' => 'permit_empty|in_list[admin,merchant]'
        ];

        $data = [
            'email' => $email,
            'password' => $password,
            'first_name' => $firstName,
            'last_name' => $lastName,
            'role' => $role
        ];

        if (!$this->validate($rules, $data)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $this->validator->getErrors()
            ])->setStatusCode(400);
        }

        $data['status'] = 'active';

        try {
            $userId = $this->userModel->insert($data);

            if ($userId) {
                $user = $this->userModel->find($userId);

                // Send welcome email
                $emailController = new Email();
                $emailController->sendWelcomeEmail(
                    $user['email'],
                    $user['first_name'] . ' ' . $user['last_name'],
                    $user['role']
                );

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'User registered successfully',
                    'data' => [
                        'user' => $user,
                        'token' => $this->generateJWT($user)
                    ]
                ])->setStatusCode(201);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to register user',
                    'model_errors' => $this->userModel->errors()
                ])->setStatusCode(500);
            }
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Registration failed: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * User Login
     */
    public function login()
    {
        if ($this->request->getMethod() !== 'POST') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ])->setStatusCode(405);
        }

        $rules = [
            'email' => 'required|valid_email',
            'password' => 'required'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $this->validator->getErrors()
            ])->setStatusCode(400);
        }

        // Get JSON input
        $json = $this->request->getJSON();
        $email = $json->email ?? $this->request->getPost('email');
        $password = $json->password ?? $this->request->getPost('password');

        // Find user by email with password hash for verification
        $userWithPassword = $this->userModel->select('*')->where('email', $email)->first();

        if (!$userWithPassword) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid credentials'
            ])->setStatusCode(401);
        }

        if (!password_verify($password, $userWithPassword['password_hash'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid credentials'
            ])->setStatusCode(401);
        }

        // Remove password hash from user data for response
        $user = $userWithPassword;
        unset($user['password_hash']);

        if ($user['status'] !== 'active') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Account is not active'
            ])->setStatusCode(401);
        }

        // Update last login (commented out due to datetime casting issue)
        // $this->userModel->update($user['id'], ['last_login_at' => new \DateTime()]);

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Login successful',
            'data' => [
                'user' => $user,
                'token' => $this->generateJWT($user)
            ]
        ]);
    }

    /**
     * Test database connection and user lookup
     */
    public function testDb()
    {
        try {
            // Test database connection
            $db = \Config\Database::connect();
            $query = $db->query('SELECT COUNT(*) as count FROM platform_users');
            $result = $query->getRow();

            // Test user model
            $users = $this->userModel->findAll();

            // Test specific user lookup
            $adminUser = $this->userModel->where('email', '<EMAIL>')->first();

            return $this->response->setJSON([
                'success' => true,
                'database_connection' => 'working',
                'total_users' => $result->count,
                'users_via_model' => count($users),
                'admin_user_found' => $adminUser ? 'yes' : 'no',
                'admin_user_data' => $adminUser
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Request Password Reset
     */
    public function forgotPassword()
    {
        if ($this->request->getMethod() !== 'POST') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ])->setStatusCode(405);
        }

        $json = $this->request->getJSON();
        $email = $json->email ?? $this->request->getPost('email');

        if (!$email || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Valid email address is required'
            ])->setStatusCode(400);
        }

        $user = $this->userModel->where('email', $email)->first();

        if (!$user) {
            // Don't reveal if email exists or not for security
            return $this->response->setJSON([
                'success' => true,
                'message' => 'If the email exists, a password reset link has been sent.'
            ]);
        }

        // Generate reset token
        $resetToken = bin2hex(random_bytes(32));

        // Store reset token in database (you'd need to create this table)
        $db = \Config\Database::connect();
        $db->table('password_reset_tokens')->insert([
            'user_id' => $user['id'],
            'token' => $resetToken,
            'expires_at' => date('Y-m-d H:i:s', strtotime('+1 hour')),
            'created_at' => date('Y-m-d H:i:s')
        ]);

        // Send reset email
        $emailController = new Email();
        $emailSent = $emailController->sendPasswordResetEmail($email, $resetToken);

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Password reset link has been sent to your email.'
        ]);
    }

    /**
     * Reset Password
     */
    public function resetPassword()
    {
        if ($this->request->getMethod() !== 'POST') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ])->setStatusCode(405);
        }

        $json = $this->request->getJSON();
        $token = $json->token ?? $this->request->getPost('token');
        $password = $json->password ?? $this->request->getPost('password');

        if (!$token || !$password) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Token and password are required'
            ])->setStatusCode(400);
        }

        if (strlen($password) < 6) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Password must be at least 6 characters long'
            ])->setStatusCode(400);
        }

        // Verify reset token
        $db = \Config\Database::connect();
        $resetRecord = $db->table('password_reset_tokens')
            ->where('token', $token)
            ->where('expires_at >', date('Y-m-d H:i:s'))
            ->where('used_at', null)
            ->get()
            ->getRow();

        if (!$resetRecord) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid or expired reset token'
            ])->setStatusCode(400);
        }

        // Update user password
        $this->userModel->update($resetRecord->user_id, [
            'password' => $password // This will be hashed by the model
        ]);

        // Mark token as used
        $db->table('password_reset_tokens')
            ->where('id', $resetRecord->id)
            ->update(['used_at' => date('Y-m-d H:i:s')]);

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Password has been reset successfully'
        ]);
    }

    /**
     * Generate JWT Token
     */
    private function generateJWT(array $user): string
    {
        $payload = [
            'iss' => base_url(),
            'aud' => base_url(),
            'iat' => time(),
            'exp' => time() + (int)(getenv('jwt.expiration') ?: 3600),
            'user_id' => $user['id'],
            'user_uuid' => $user['uuid'],
            'email' => $user['email'],
            'role' => $user['role']
        ];

        return JWT::encode($payload, $this->jwtSecret, $this->jwtAlgorithm);
    }
}
