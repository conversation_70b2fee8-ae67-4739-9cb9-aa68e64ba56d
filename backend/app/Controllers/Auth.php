<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\PlatformUser;
use Firebase\JWT\JWT;

class Auth extends BaseController
{
    protected $userModel;
    protected $jwtSecret;
    protected $jwtAlgorithm;

    public function __construct()
    {
        $this->userModel = new PlatformUser();
        $this->jwtSecret = getenv('jwt.secret') ?: 'your-default-secret-key';
        $this->jwtAlgorithm = getenv('jwt.algorithm') ?: 'HS256';
    }

    /**
     * User Registration
     */
    public function register()
    {
        if ($this->request->getMethod() !== 'POST') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ])->setStatusCode(405);
        }

        $rules = [
            'email' => 'required|valid_email|is_unique[platform_users.email]',
            'password' => 'required|min_length[8]',
            'first_name' => 'required|min_length[2]|max_length[100]',
            'last_name' => 'required|min_length[2]|max_length[100]',
            'role' => 'in_list[admin,merchant]'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $this->validator->getErrors()
            ])->setStatusCode(400);
        }

        $data = [
            'email' => $this->request->getPost('email'),
            'password' => $this->request->getPost('password'),
            'first_name' => $this->request->getPost('first_name'),
            'last_name' => $this->request->getPost('last_name'),
            'role' => $this->request->getPost('role') ?: 'merchant',
            'status' => 'active'
        ];

        try {
            $userId = $this->userModel->insert($data);

            if ($userId) {
                $user = $this->userModel->find($userId);

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'User registered successfully',
                    'data' => [
                        'user' => $user,
                        'token' => $this->generateJWT($user)
                    ]
                ])->setStatusCode(201);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to register user'
                ])->setStatusCode(500);
            }
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Registration failed: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * User Login
     */
    public function login()
    {
        if ($this->request->getMethod() !== 'POST') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ])->setStatusCode(405);
        }

        $rules = [
            'email' => 'required|valid_email',
            'password' => 'required'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $this->validator->getErrors()
            ])->setStatusCode(400);
        }

        $email = $this->request->getPost('email');
        $password = $this->request->getPost('password');

        // Find user by email (need to get password hash for verification)
        $user = $this->userModel->where('email', $email)->first();

        if (!$user) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid credentials'
            ])->setStatusCode(401);
        }

        // Get user with password hash for verification
        $userWithPassword = $this->userModel->select('*')->where('email', $email)->first();

        if (!password_verify($password, $userWithPassword['password_hash'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid credentials'
            ])->setStatusCode(401);
        }

        if ($user['status'] !== 'active') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Account is not active'
            ])->setStatusCode(401);
        }

        // Update last login
        $this->userModel->update($user['id'], ['last_login_at' => date('Y-m-d H:i:s')]);

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Login successful',
            'data' => [
                'user' => $user,
                'token' => $this->generateJWT($user)
            ]
        ]);
    }

    /**
     * Generate JWT Token
     */
    private function generateJWT(array $user): string
    {
        $payload = [
            'iss' => base_url(),
            'aud' => base_url(),
            'iat' => time(),
            'exp' => time() + (int)(getenv('jwt.expiration') ?: 3600),
            'user_id' => $user['id'],
            'user_uuid' => $user['uuid'],
            'email' => $user['email'],
            'role' => $user['role']
        ];

        return JWT::encode($payload, $this->jwtSecret, $this->jwtAlgorithm);
    }
}
