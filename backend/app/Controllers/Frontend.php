<?php

namespace App\Controllers;

use App\Controllers\BaseController;

class Frontend extends BaseController
{
    /**
     * Admin Dashboard
     */
    public function adminDashboard()
    {
        return $this->serveTemplate('admin_dashboard.html');
    }

    /**
     * Admin Login Page
     */
    public function adminLogin()
    {
        return $this->serveTemplate('admin_login.html');
    }

    /**
     * Admin Register Page
     */
    public function adminRegister()
    {
        return $this->serveTemplate('admin_register.html');
    }

    /**
     * Shops Management Page
     */
    public function shops()
    {
        return $this->serveTemplate('admin_shops.html');
    }

    /**
     * Shop Details Page
     */
    public function shopDetails($shopId)
    {
        return $this->serveTemplate('admin_shop_details.html', ['shop_id' => $shopId]);
    }

    /**
     * Serve HTML template
     */
    private function serveTemplate($template, $data = [])
    {
        $templatePath = FCPATH . '../frontend/templates/' . $template;

        if (!file_exists($templatePath)) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException("Template not found: $template");
        }

        $content = file_get_contents($templatePath);

        // Simple template variable replacement
        foreach ($data as $key => $value) {
            $content = str_replace("{{$key}}", $value, $content);
        }

        return $this->response->setBody($content);
    }
}
