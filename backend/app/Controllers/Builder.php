<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\ShopModel;

class Builder extends BaseController
{
    protected $shopModel;

    public function __construct()
    {
        $this->shopModel = new ShopModel();
    }

    /**
     * Get available components for the builder
     */
    public function components()
    {
        $components = [
            'layout' => [
                [
                    'id' => 'header',
                    'name' => 'Header',
                    'description' => 'Navigation header with logo and menu',
                    'icon' => 'fas fa-window-maximize',
                    'category' => 'layout',
                    'template' => 'header.html',
                    'settings' => [
                        'logo' => ['type' => 'image', 'label' => 'Logo'],
                        'menu_items' => ['type' => 'array', 'label' => 'Menu Items'],
                        'background_color' => ['type' => 'color', 'label' => 'Background Color'],
                        'text_color' => ['type' => 'color', 'label' => 'Text Color']
                    ]
                ],
                [
                    'id' => 'footer',
                    'name' => 'Footer',
                    'description' => 'Footer with links and contact information',
                    'icon' => 'fas fa-window-minimize',
                    'category' => 'layout',
                    'template' => 'footer.html',
                    'settings' => [
                        'copyright_text' => ['type' => 'text', 'label' => 'Copyright Text'],
                        'social_links' => ['type' => 'array', 'label' => 'Social Links'],
                        'background_color' => ['type' => 'color', 'label' => 'Background Color']
                    ]
                ]
            ],
            'content' => [
                [
                    'id' => 'hero',
                    'name' => 'Hero Section',
                    'description' => 'Large banner with call-to-action',
                    'icon' => 'fas fa-image',
                    'category' => 'content',
                    'template' => 'hero.html',
                    'settings' => [
                        'title' => ['type' => 'text', 'label' => 'Title'],
                        'subtitle' => ['type' => 'text', 'label' => 'Subtitle'],
                        'background_image' => ['type' => 'image', 'label' => 'Background Image'],
                        'button_text' => ['type' => 'text', 'label' => 'Button Text'],
                        'button_link' => ['type' => 'text', 'label' => 'Button Link']
                    ]
                ],
                [
                    'id' => 'text_block',
                    'name' => 'Text Block',
                    'description' => 'Rich text content block',
                    'icon' => 'fas fa-align-left',
                    'category' => 'content',
                    'template' => 'text_block.html',
                    'settings' => [
                        'content' => ['type' => 'richtext', 'label' => 'Content'],
                        'alignment' => ['type' => 'select', 'label' => 'Alignment', 'options' => ['left', 'center', 'right']]
                    ]
                ],
                [
                    'id' => 'image_gallery',
                    'name' => 'Image Gallery',
                    'description' => 'Responsive image gallery',
                    'icon' => 'fas fa-images',
                    'category' => 'content',
                    'template' => 'image_gallery.html',
                    'settings' => [
                        'images' => ['type' => 'image_array', 'label' => 'Images'],
                        'columns' => ['type' => 'number', 'label' => 'Columns', 'min' => 1, 'max' => 6]
                    ]
                ]
            ],
            'ecommerce' => [
                [
                    'id' => 'product_grid',
                    'name' => 'Product Grid',
                    'description' => 'Display products in a grid layout',
                    'icon' => 'fas fa-th',
                    'category' => 'ecommerce',
                    'template' => 'product_grid.html',
                    'settings' => [
                        'products_per_row' => ['type' => 'number', 'label' => 'Products per Row', 'min' => 1, 'max' => 6],
                        'show_price' => ['type' => 'boolean', 'label' => 'Show Price'],
                        'show_add_to_cart' => ['type' => 'boolean', 'label' => 'Show Add to Cart']
                    ]
                ],
                [
                    'id' => 'featured_products',
                    'name' => 'Featured Products',
                    'description' => 'Showcase featured products',
                    'icon' => 'fas fa-star',
                    'category' => 'ecommerce',
                    'template' => 'featured_products.html',
                    'settings' => [
                        'title' => ['type' => 'text', 'label' => 'Section Title'],
                        'product_count' => ['type' => 'number', 'label' => 'Number of Products', 'min' => 1, 'max' => 12]
                    ]
                ],
                [
                    'id' => 'shopping_cart',
                    'name' => 'Shopping Cart',
                    'description' => 'Shopping cart widget',
                    'icon' => 'fas fa-shopping-cart',
                    'category' => 'ecommerce',
                    'template' => 'shopping_cart.html',
                    'settings' => [
                        'show_item_count' => ['type' => 'boolean', 'label' => 'Show Item Count'],
                        'show_total' => ['type' => 'boolean', 'label' => 'Show Total']
                    ]
                ]
            ],
            'forms' => [
                [
                    'id' => 'contact_form',
                    'name' => 'Contact Form',
                    'description' => 'Contact form with validation',
                    'icon' => 'fas fa-envelope',
                    'category' => 'forms',
                    'template' => 'contact_form.html',
                    'settings' => [
                        'title' => ['type' => 'text', 'label' => 'Form Title'],
                        'fields' => ['type' => 'array', 'label' => 'Form Fields'],
                        'submit_text' => ['type' => 'text', 'label' => 'Submit Button Text']
                    ]
                ],
                [
                    'id' => 'newsletter_signup',
                    'name' => 'Newsletter Signup',
                    'description' => 'Email newsletter subscription form',
                    'icon' => 'fas fa-newspaper',
                    'category' => 'forms',
                    'template' => 'newsletter_signup.html',
                    'settings' => [
                        'title' => ['type' => 'text', 'label' => 'Title'],
                        'description' => ['type' => 'text', 'label' => 'Description'],
                        'button_text' => ['type' => 'text', 'label' => 'Button Text']
                    ]
                ]
            ]
        ];

        return $this->response->setJSON([
            'success' => true,
            'data' => $components
        ]);
    }

    /**
     * Get page structure for a shop
     */
    public function getPage($shopId, $pageId = 'home')
    {
        try {
            $shop = $this->shopModel->find($shopId);
            if (!$shop) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Shop not found'
                ])->setStatusCode(404);
            }

            // For now, return a default page structure
            // In production, this would be stored in database
            $pageStructure = [
                'id' => $pageId,
                'name' => ucfirst($pageId),
                'components' => [
                    [
                        'id' => 'header-1',
                        'type' => 'header',
                        'settings' => [
                            'logo' => '/frontend/assets/images/logo.png',
                            'menu_items' => [
                                ['label' => 'Home', 'url' => '/'],
                                ['label' => 'Products', 'url' => '/products'],
                                ['label' => 'About', 'url' => '/about'],
                                ['label' => 'Contact', 'url' => '/contact']
                            ],
                            'background_color' => '#ffffff',
                            'text_color' => '#333333'
                        ]
                    ],
                    [
                        'id' => 'hero-1',
                        'type' => 'hero',
                        'settings' => [
                            'title' => 'Welcome to ' . $shop['name'],
                            'subtitle' => $shop['description'] ?: 'Your one-stop shop for amazing products',
                            'background_image' => '/frontend/assets/images/hero-bg.jpg',
                            'button_text' => 'Shop Now',
                            'button_link' => '/products'
                        ]
                    ],
                    [
                        'id' => 'featured-products-1',
                        'type' => 'featured_products',
                        'settings' => [
                            'title' => 'Featured Products',
                            'product_count' => 8
                        ]
                    ],
                    [
                        'id' => 'footer-1',
                        'type' => 'footer',
                        'settings' => [
                            'copyright_text' => '© 2024 ' . $shop['name'] . '. All rights reserved.',
                            'social_links' => [],
                            'background_color' => '#f8f9fa'
                        ]
                    ]
                ]
            ];

            return $this->response->setJSON([
                'success' => true,
                'data' => $pageStructure
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to load page'
            ])->setStatusCode(500);
        }
    }

    /**
     * Save page structure
     */
    public function savePage($shopId)
    {
        if ($this->request->getMethod() !== 'POST') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ])->setStatusCode(405);
        }

        try {
            $shop = $this->shopModel->find($shopId);
            if (!$shop) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Shop not found'
                ])->setStatusCode(404);
            }

            $json = $this->request->getJSON();
            $pageData = [
                'id' => $json->id ?? 'home',
                'name' => $json->name ?? 'Home',
                'components' => $json->components ?? []
            ];

            // TODO: Save to database
            // For now, we'll just return success

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Page saved successfully',
                'data' => $pageData
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to save page'
            ])->setStatusCode(500);
        }
    }

    /**
     * Preview shop
     */
    public function preview($shopId)
    {
        try {
            $shop = $this->shopModel->find($shopId);
            if (!$shop) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Shop not found'
                ])->setStatusCode(404);
            }

            // Generate preview URL
            $previewUrl = base_url("preview/{$shop['subdomain']}");

            return $this->response->setJSON([
                'success' => true,
                'data' => [
                    'preview_url' => $previewUrl,
                    'shop' => $shop
                ]
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to generate preview'
            ])->setStatusCode(500);
        }
    }

    /**
     * Publish shop
     */
    public function publish($shopId)
    {
        if ($this->request->getMethod() !== 'POST') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ])->setStatusCode(405);
        }

        try {
            $shop = $this->shopModel->find($shopId);
            if (!$shop) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Shop not found'
                ])->setStatusCode(404);
            }

            // Update shop status to published
            $this->shopModel->update($shopId, ['status' => 'active']);

            // Generate live URLs
            $liveUrl = "https://{$shop['subdomain']}.shopci.test";
            $customUrl = $shop['custom_domain'] ? "https://{$shop['custom_domain']}" : null;

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Shop published successfully',
                'data' => [
                    'live_url' => $liveUrl,
                    'custom_url' => $customUrl,
                    'shop' => $shop
                ]
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to publish shop'
            ])->setStatusCode(500);
        }
    }
}
