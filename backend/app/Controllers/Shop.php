<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\ShopModel;
use App\Models\PlatformUser;

class Shop extends BaseController
{
    protected $shopModel;
    protected $userModel;

    public function __construct()
    {
        $this->shopModel = new ShopModel();
        $this->userModel = new PlatformUser();
    }

    /**
     * Get all shops (Admin only)
     */
    public function index()
    {
        // TODO: Add JWT authentication middleware

        try {
            $shops = $this->shopModel->getShopsWithOwners();

            return $this->response->setJSON([
                'success' => true,
                'data' => $shops
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch shops'
            ])->setStatusCode(500);
        }
    }

    /**
     * Get user's shops
     */
    public function myShops()
    {
        // TODO: Get user ID from JWT token
        $userId = 1; // Temporary - replace with JWT user ID

        try {
            $shops = $this->shopModel->where('owner_id', $userId)->findAll();

            return $this->response->setJSON([
                'success' => true,
                'data' => $shops
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch your shops'
            ])->setStatusCode(500);
        }
    }

    /**
     * Get single shop
     */
    public function show($id)
    {
        try {
            $shop = $this->shopModel->getShopWithDetails($id);

            if (!$shop) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Shop not found'
                ])->setStatusCode(404);
            }

            return $this->response->setJSON([
                'success' => true,
                'data' => $shop
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch shop'
            ])->setStatusCode(500);
        }
    }

    /**
     * Create new shop
     */
    public function create()
    {
        if ($this->request->getMethod() !== 'POST') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ])->setStatusCode(405);
        }

        $json = $this->request->getJSON();
        $subdomain = $json->subdomain ?? '';
        $data = [
            'name' => $json->name ?? '',
            'description' => $json->description ?? '',
            'subdomain' => $subdomain,
            'custom_domain' => $json->custom_domain ?? null,
            'theme' => $json->theme ?? 'default',
            'owner_id' => 1, // TODO: Get from JWT token
            'status' => 'active',
            'settings' => json_encode([]),
            // Legacy fields for compatibility
            'shop_name' => $json->name ?? '',
            'shop_domain' => $subdomain . '.shopci.test',
            'database_name' => 'shopci_' . $subdomain
        ];

        $rules = [
            'name' => 'required|min_length[2]|max_length[100]',
            'subdomain' => 'required|min_length[3]|max_length[50]|alpha_dash|is_unique[shops.subdomain]',
            'description' => 'permit_empty|max_length[500]',
            'theme' => 'permit_empty|in_list[default,modern,classic,minimal]'
        ];

        if (!$this->validate($rules, $data)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $this->validator->getErrors()
            ])->setStatusCode(400);
        }

        try {
            $shopId = $this->shopModel->insert($data);

            if ($shopId) {
                $shop = $this->shopModel->find($shopId);

                // Create default shop database
                $this->createShopDatabase($shop['subdomain']);

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Shop created successfully',
                    'data' => $shop
                ])->setStatusCode(201);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to create shop',
                    'errors' => $this->shopModel->errors()
                ])->setStatusCode(500);
            }
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to create shop: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Update shop
     */
    public function update($id)
    {
        if ($this->request->getMethod() !== 'PUT' && $this->request->getMethod() !== 'POST') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ])->setStatusCode(405);
        }

        $shop = $this->shopModel->find($id);
        if (!$shop) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Shop not found'
            ])->setStatusCode(404);
        }

        $json = $this->request->getJSON();
        $data = [
            'name' => $json->name ?? $shop['name'],
            'description' => $json->description ?? $shop['description'],
            'custom_domain' => $json->custom_domain ?? $shop['custom_domain'],
            'theme' => $json->theme ?? $shop['theme'],
            'status' => $json->status ?? $shop['status']
        ];

        $rules = [
            'name' => 'required|min_length[2]|max_length[100]',
            'description' => 'permit_empty|max_length[500]',
            'theme' => 'permit_empty|in_list[default,modern,classic,minimal]',
            'status' => 'permit_empty|in_list[active,inactive,suspended]'
        ];

        if (!$this->validate($rules, $data)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $this->validator->getErrors()
            ])->setStatusCode(400);
        }

        try {
            $this->shopModel->update($id, $data);
            $updatedShop = $this->shopModel->find($id);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Shop updated successfully',
                'data' => $updatedShop
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to update shop'
            ])->setStatusCode(500);
        }
    }

    /**
     * Delete shop
     */
    public function delete($id)
    {
        if ($this->request->getMethod() !== 'DELETE') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ])->setStatusCode(405);
        }

        $shop = $this->shopModel->find($id);
        if (!$shop) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Shop not found'
            ])->setStatusCode(404);
        }

        try {
            $this->shopModel->delete($id);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Shop deleted successfully'
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to delete shop'
            ])->setStatusCode(500);
        }
    }

    /**
     * Create shop database (for multi-tenant architecture)
     */
    private function createShopDatabase($subdomain)
    {
        try {
            $db = \Config\Database::connect();
            $dbName = 'shopci_' . $subdomain;

            // Create database
            $db->query("CREATE DATABASE IF NOT EXISTS `{$dbName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci");

            // TODO: Run shop-specific migrations
            log_message('info', "Created database for shop: {$dbName}");

            return true;
        } catch (\Exception $e) {
            log_message('error', "Failed to create shop database: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get shop themes
     */
    public function themes()
    {
        $themes = [
            [
                'id' => 'default',
                'name' => 'Default',
                'description' => 'Clean and simple design perfect for any business',
                'preview_image' => '/frontend/assets/images/themes/default.jpg',
                'features' => ['Responsive', 'SEO Optimized', 'Fast Loading'],
                'price' => 0
            ],
            [
                'id' => 'modern',
                'name' => 'Modern',
                'description' => 'Contemporary design with bold typography and animations',
                'preview_image' => '/frontend/assets/images/themes/modern.jpg',
                'features' => ['Animations', 'Modern UI', 'Mobile First'],
                'price' => 29
            ],
            [
                'id' => 'classic',
                'name' => 'Classic',
                'description' => 'Timeless design that works for traditional businesses',
                'preview_image' => '/frontend/assets/images/themes/classic.jpg',
                'features' => ['Professional', 'Clean Layout', 'Easy Navigation'],
                'price' => 19
            ],
            [
                'id' => 'minimal',
                'name' => 'Minimal',
                'description' => 'Minimalist design focusing on content and products',
                'preview_image' => '/frontend/assets/images/themes/minimal.jpg',
                'features' => ['Minimal Design', 'Fast Performance', 'Content Focus'],
                'price' => 39
            ]
        ];

        return $this->response->setJSON([
            'success' => true,
            'data' => $themes
        ]);
    }
}
