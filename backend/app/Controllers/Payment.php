<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\OrderModel;
use App\Models\ProductModel;

class Payment extends BaseController
{
    protected $orderModel;
    protected $productModel;

    public function __construct()
    {
        $this->orderModel = new OrderModel();
        $this->productModel = new ProductModel();
    }

    /**
     * Create payment intent (Stripe-like)
     */
    public function createIntent()
    {
        if ($this->request->getMethod() !== 'POST') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ])->setStatusCode(405);
        }

        $json = $this->request->getJSON();
        $amount = $json->amount ?? 0;
        $currency = $json->currency ?? 'USD';
        $shopId = $json->shop_id ?? 1;

        if ($amount <= 0) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid amount'
            ])->setStatusCode(400);
        }

        try {
            // Generate payment intent
            $intentId = 'pi_' . uniqid();
            $clientSecret = $intentId . '_secret_' . bin2hex(random_bytes(16));

            // In a real implementation, you would:
            // 1. Create payment intent with Stripe/PayPal/etc.
            // 2. Store intent in database
            // 3. Return client secret for frontend

            return $this->response->setJSON([
                'success' => true,
                'data' => [
                    'intent_id' => $intentId,
                    'client_secret' => $clientSecret,
                    'amount' => $amount,
                    'currency' => $currency,
                    'status' => 'requires_payment_method'
                ]
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to create payment intent'
            ])->setStatusCode(500);
        }
    }

    /**
     * Process payment
     */
    public function processPayment()
    {
        if ($this->request->getMethod() !== 'POST') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ])->setStatusCode(405);
        }

        $json = $this->request->getJSON();
        $intentId = $json->intent_id ?? '';
        $paymentMethod = $json->payment_method ?? '';
        $orderData = $json->order_data ?? null;

        if (empty($intentId) || empty($paymentMethod)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Payment intent ID and payment method are required'
            ])->setStatusCode(400);
        }

        try {
            // Simulate payment processing
            $paymentSuccess = $this->simulatePaymentProcessing($paymentMethod);

            if ($paymentSuccess) {
                // Create order
                $orderId = $this->createOrder($orderData, $intentId, $paymentMethod);

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Payment processed successfully',
                    'data' => [
                        'order_id' => $orderId,
                        'payment_status' => 'paid',
                        'transaction_id' => 'txn_' . uniqid()
                    ]
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Payment failed',
                    'error_code' => 'payment_declined'
                ])->setStatusCode(400);
            }
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Payment processing failed'
            ])->setStatusCode(500);
        }
    }

    /**
     * Get payment methods
     */
    public function getPaymentMethods($shopId)
    {
        try {
            // In a real implementation, this would come from shop settings
            $paymentMethods = [
                [
                    'id' => 'stripe',
                    'name' => 'Credit/Debit Card',
                    'description' => 'Pay securely with your credit or debit card',
                    'icon' => 'fas fa-credit-card',
                    'enabled' => true,
                    'fees' => '2.9% + $0.30'
                ],
                [
                    'id' => 'paypal',
                    'name' => 'PayPal',
                    'description' => 'Pay with your PayPal account',
                    'icon' => 'fab fa-paypal',
                    'enabled' => true,
                    'fees' => '2.9% + $0.30'
                ],
                [
                    'id' => 'apple_pay',
                    'name' => 'Apple Pay',
                    'description' => 'Pay with Touch ID or Face ID',
                    'icon' => 'fab fa-apple-pay',
                    'enabled' => true,
                    'fees' => '2.9% + $0.30'
                ],
                [
                    'id' => 'google_pay',
                    'name' => 'Google Pay',
                    'description' => 'Pay with Google Pay',
                    'icon' => 'fab fa-google-pay',
                    'enabled' => true,
                    'fees' => '2.9% + $0.30'
                ],
                [
                    'id' => 'bank_transfer',
                    'name' => 'Bank Transfer',
                    'description' => 'Direct bank transfer',
                    'icon' => 'fas fa-university',
                    'enabled' => false,
                    'fees' => 'Free'
                ]
            ];

            return $this->response->setJSON([
                'success' => true,
                'data' => $paymentMethods
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch payment methods'
            ])->setStatusCode(500);
        }
    }

    /**
     * Simulate payment processing
     */
    private function simulatePaymentProcessing($paymentMethod)
    {
        // Simulate different success rates for different payment methods
        $successRates = [
            'stripe' => 0.95,
            'paypal' => 0.92,
            'apple_pay' => 0.98,
            'google_pay' => 0.96,
            'bank_transfer' => 0.85
        ];

        $successRate = $successRates[$paymentMethod] ?? 0.90;

        // Add some delay to simulate real payment processing
        usleep(500000); // 0.5 seconds

        return (mt_rand() / mt_getrandmax()) < $successRate;
    }

    /**
     * Create order from payment data
     */
    private function createOrder($orderData, $intentId, $paymentMethod)
    {
        if (!$orderData) {
            throw new \Exception('Order data is required');
        }

        $data = [
            'shop_id' => $orderData->shop_id ?? 1,
            'customer_id' => $orderData->customer_id ?? null,
            'subtotal' => $orderData->subtotal ?? 0,
            'tax_amount' => $orderData->tax_amount ?? 0,
            'shipping_amount' => $orderData->shipping_amount ?? 0,
            'discount_amount' => $orderData->discount_amount ?? 0,
            'total_amount' => $orderData->total_amount ?? 0,
            'currency' => $orderData->currency ?? 'USD',
            'status' => 'processing',
            'payment_status' => 'paid',
            'payment_method' => $paymentMethod,
            'payment_gateway' => $this->getGatewayFromMethod($paymentMethod),
            'billing_address' => json_encode($orderData->billing_address ?? []),
            'shipping_address' => json_encode($orderData->shipping_address ?? []),
            'metadata' => json_encode([
                'payment_intent_id' => $intentId,
                'items' => $orderData->items ?? []
            ])
        ];

        $orderId = $this->orderModel->insert($data);

        // Update product stock
        if (isset($orderData->items)) {
            foreach ($orderData->items as $item) {
                $this->productModel->updateStock($item->product_id, $item->quantity, 'decrease');
            }
        }

        return $orderId;
    }

    /**
     * Get payment gateway from method
     */
    private function getGatewayFromMethod($method)
    {
        $gateways = [
            'stripe' => 'stripe',
            'paypal' => 'paypal',
            'apple_pay' => 'stripe',
            'google_pay' => 'stripe',
            'bank_transfer' => 'manual'
        ];

        return $gateways[$method] ?? 'unknown';
    }

    /**
     * Webhook handler for payment notifications
     */
    public function webhook($gateway)
    {
        if ($this->request->getMethod() !== 'POST') {
            return $this->response->setStatusCode(405);
        }

        try {
            $payload = $this->request->getBody();
            $signature = $this->request->getHeaderLine('Stripe-Signature') ?:
                        $this->request->getHeaderLine('PayPal-Transmission-Sig');

            // In a real implementation, you would:
            // 1. Verify webhook signature
            // 2. Parse webhook payload
            // 3. Update order status based on payment status
            // 4. Send confirmation emails

            log_message('info', "Webhook received from {$gateway}: " . $payload);

            return $this->response->setJSON(['received' => true]);
        } catch (\Exception $e) {
            log_message('error', "Webhook error: " . $e->getMessage());
            return $this->response->setStatusCode(400);
        }
    }

    /**
     * Refund payment
     */
    public function refund($orderId)
    {
        if ($this->request->getMethod() !== 'POST') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ])->setStatusCode(405);
        }

        $order = $this->orderModel->find($orderId);
        if (!$order) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Order not found'
            ])->setStatusCode(404);
        }

        if ($order['payment_status'] !== 'paid') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Order is not paid'
            ])->setStatusCode(400);
        }

        $json = $this->request->getJSON();
        $amount = $json->amount ?? $order['total_amount'];
        $reason = $json->reason ?? 'Customer request';

        try {
            // In a real implementation, you would call the payment gateway's refund API
            $refundSuccess = $this->simulateRefundProcessing($order['payment_gateway'], $amount);

            if ($refundSuccess) {
                // Update order status
                $newStatus = $amount >= $order['total_amount'] ? 'refunded' : 'partially_refunded';
                $this->orderModel->update($orderId, [
                    'payment_status' => $newStatus,
                    'notes' => "Refund: {$reason}"
                ]);

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Refund processed successfully',
                    'data' => [
                        'refund_amount' => $amount,
                        'refund_id' => 'ref_' . uniqid()
                    ]
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Refund failed'
                ])->setStatusCode(400);
            }
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Refund processing failed'
            ])->setStatusCode(500);
        }
    }

    /**
     * Simulate refund processing
     */
    private function simulateRefundProcessing($gateway, $amount)
    {
        // Simulate refund processing delay
        usleep(300000); // 0.3 seconds

        // Most refunds succeed
        return (mt_rand() / mt_getrandmax()) < 0.95;
    }
}
