<?php

namespace App\Controllers;

use App\Controllers\BaseController;

class Health extends BaseController
{
    /**
     * Health check endpoint
     */
    public function index()
    {
        $db = \Config\Database::connect();

        $health = [
            'status' => 'healthy',
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '1.0.0',
            'services' => [
                'database' => 'unknown',
                'cache' => 'unknown',
                'session' => 'unknown'
            ]
        ];

        // Check database connection
        try {
            $db->query('SELECT 1');
            $health['services']['database'] = 'connected';
        } catch (\Exception $e) {
            $health['services']['database'] = 'disconnected';
            $health['status'] = 'degraded';
        }

        // Check cache
        try {
            $cache = \Config\Services::cache();
            $cache->save('health_check', 'test', 1);
            $health['services']['cache'] = 'active';
        } catch (\Exception $e) {
            $health['services']['cache'] = 'inactive';
        }

        // Check session
        try {
            session();
            $health['services']['session'] = 'active';
        } catch (\Exception $e) {
            $health['services']['session'] = 'inactive';
        }

        return $this->response->setJSON($health);
    }

    /**
     * Simple ping endpoint
     */
    public function ping()
    {
        return $this->response->setJSON([
            'message' => 'pong',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
}
