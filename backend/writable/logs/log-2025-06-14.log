CRITICAL - 2025-06-14 22:52:34 --> CodeIgniter\Exceptions\InvalidArgumentException: [CodeIgniter\DataCaster\Cast\DatetimeCast] Invalid value type: string, and its value: '2025-06-14 22:52:34'
[Method: POST, Route: api/auth/login]
in SYSTEMPATH/DataCaster/Cast/BaseCast.php on line 43.
 1 SYSTEMPATH/DataCaster/Cast/DatetimeCast.php(57): CodeIgniter\DataCaster\Cast\BaseCast::invalidTypeValueError('2025-06-14 22:52:34')
 2 SYSTEMPATH/DataCaster/DataCaster.php(186): CodeIgniter\DataCaster\Cast\DatetimeCast::set('2025-06-14 22:52:34', [], Object(CodeIgniter\Database\MySQLi\Connection))
 3 SYSTEMPATH/DataConverter/DataConverter.php(98): CodeIgniter\DataCaster\DataCaster->castAs('2025-06-14 22:52:34', 'last_login_at', 'set')
 4 SYSTEMPATH/BaseModel.php(1846): CodeIgniter\DataConverter\DataConverter->toDataSource([...])
 5 SYSTEMPATH/BaseModel.php(1000): CodeIgniter\BaseModel->transformDataToArray([...], 'update')
 6 SYSTEMPATH/Model.php(863): CodeIgniter\BaseModel->update([...], [...])
 7 APPPATH/Controllers/Auth.php(165): CodeIgniter\Model->update('1', [...])
 8 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Auth->login()
 9 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
10 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-14 22:56:43 --> CodeIgniter\Filters\Exceptions\FilterException: "toolbar" filter must have a matching alias defined.
[Method: GET, Route: admin/login]
in SYSTEMPATH/Filters/Filters.php on line 402.
 1 SYSTEMPATH/Filters/Filters.php(402): CodeIgniter\Filters\Exceptions\FilterException::forNoAlias('toolbar')
 2 SYSTEMPATH/Filters/Filters.php(319): CodeIgniter\Filters\Filters->getRequiredFilters('after')
 3 SYSTEMPATH/Filters/Filters.php(353): CodeIgniter\Filters\Filters->getRequiredClasses('after')
 4 SYSTEMPATH/CodeIgniter.php(407): CodeIgniter\Filters\Filters->runRequired('after')
 5 SYSTEMPATH/CodeIgniter.php(368): CodeIgniter\CodeIgniter->runRequiredAfterFilters(Object(CodeIgniter\Filters\Filters))
 6 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 8 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-14 23:09:08 --> CodeIgniter\Database\Exceptions\DataException: There is no data to update.
[Method: POST, Route: api/auth/reset-password]
in SYSTEMPATH/BaseModel.php on line 1014.
 1 SYSTEMPATH/BaseModel.php(1014): CodeIgniter\Database\Exceptions\DataException::forEmptyDataset('update')
 2 SYSTEMPATH/Model.php(863): CodeIgniter\BaseModel->update([...], [])
 3 APPPATH/Controllers/Auth.php(311): CodeIgniter\Model->update('1', [...])
 4 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Auth->resetPassword()
 5 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 6 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-14 23:09:39 --> CodeIgniter\Exceptions\InvalidArgumentException: [CodeIgniter\DataCaster\Cast\DatetimeCast] Invalid value type: string, and its value: '2025-06-14 23:09:39'
[Method: POST, Route: api/auth/login]
in SYSTEMPATH/DataCaster/Cast/BaseCast.php on line 43.
 1 SYSTEMPATH/DataCaster/Cast/DatetimeCast.php(57): CodeIgniter\DataCaster\Cast\BaseCast::invalidTypeValueError('2025-06-14 23:09:39')
 2 SYSTEMPATH/DataCaster/DataCaster.php(186): CodeIgniter\DataCaster\Cast\DatetimeCast::set('2025-06-14 23:09:39', [], Object(CodeIgniter\Database\MySQLi\Connection))
 3 SYSTEMPATH/DataConverter/DataConverter.php(98): CodeIgniter\DataCaster\DataCaster->castAs('2025-06-14 23:09:39', 'last_login_at', 'set')
 4 SYSTEMPATH/BaseModel.php(1846): CodeIgniter\DataConverter\DataConverter->toDataSource([...])
 5 SYSTEMPATH/BaseModel.php(1000): CodeIgniter\BaseModel->transformDataToArray([...], 'update')
 6 SYSTEMPATH/Model.php(863): CodeIgniter\BaseModel->update([...], [...])
 7 APPPATH/Controllers/Auth.php(165): CodeIgniter\Model->update('1', [...])
 8 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Auth->login()
 9 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
10 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-14 23:10:12 --> CodeIgniter\Exceptions\InvalidArgumentException: [CodeIgniter\DataCaster\Cast\DatetimeCast] Invalid value type: DateTime
[Method: POST, Route: api/auth/login]
in SYSTEMPATH/DataCaster/Cast/BaseCast.php on line 43.
 1 SYSTEMPATH/DataCaster/Cast/DatetimeCast.php(57): CodeIgniter\DataCaster\Cast\BaseCast::invalidTypeValueError(Object(DateTime))
 2 SYSTEMPATH/DataCaster/DataCaster.php(186): CodeIgniter\DataCaster\Cast\DatetimeCast::set(Object(DateTime), [], Object(CodeIgniter\Database\MySQLi\Connection))
 3 SYSTEMPATH/DataConverter/DataConverter.php(98): CodeIgniter\DataCaster\DataCaster->castAs(Object(DateTime), 'last_login_at', 'set')
 4 SYSTEMPATH/BaseModel.php(1846): CodeIgniter\DataConverter\DataConverter->toDataSource([...])
 5 SYSTEMPATH/BaseModel.php(1000): CodeIgniter\BaseModel->transformDataToArray([...], 'update')
 6 SYSTEMPATH/Model.php(863): CodeIgniter\BaseModel->update([...], [...])
 7 APPPATH/Controllers/Auth.php(165): CodeIgniter\Model->update('1', [...])
 8 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Auth->login()
 9 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
10 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
ERROR - 2025-06-14 23:18:02 --> mysqli_sql_exception: Unknown column 'subdomain' in 'WHERE' in /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(327): mysqli->query('SELECT 1\nFROM `...', 0)
#1 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1\nFROM `...')
#2 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1\nFROM `...')
#3 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1\nFROM `...', Array, false)
#4 /var/www/html/vendor/codeigniter4/framework/system/Validation/Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
#5 /var/www/html/vendor/codeigniter4/framework/system/Validation/StrictRules/Rules.php(187): CodeIgniter\Validation\Rules->is_unique('testshop', 'shops.subdomain', Array)
#6 /var/www/html/vendor/codeigniter4/framework/system/Validation/Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('testshop', 'shops.subdomain', Array, NULL, 'subdomain')
#7 /var/www/html/vendor/codeigniter4/framework/system/Validation/Validation.php(201): CodeIgniter\Validation\Validation->processRules('subdomain', 'subdomain', 'testshop', Array, Array, 'subdomain')
#8 /var/www/html/vendor/codeigniter4/framework/system/Controller.php(138): CodeIgniter\Validation\Validation->run()
#9 /var/www/html/app/Controllers/Shop.php(122): CodeIgniter\Controller->validate(Array, Array)
#10 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(933): App\Controllers\Shop->create()
#11 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Shop))
#12 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#13 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(334): CodeIgniter\CodeIgniter->run()
#14 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#15 /var/www/html/public/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#16 {main}
CRITICAL - 2025-06-14 23:18:02 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'subdomain' in 'WHERE'
[Method: POST, Route: api/shops]
in SYSTEMPATH/Database/BaseConnection.php on line 692.
 1 SYSTEMPATH/Database/BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `shops`
WHERE `subdomain` = :subdomain:
 LIMIT 1', [...], false)
 2 SYSTEMPATH/Validation/Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH/Validation/StrictRules/Rules.php(187): CodeIgniter\Validation\Rules->is_unique('testshop', 'shops.subdomain', [...])
 4 SYSTEMPATH/Validation/Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('testshop', 'shops.subdomain', [...], null, 'subdomain')
 5 SYSTEMPATH/Validation/Validation.php(201): CodeIgniter\Validation\Validation->processRules('subdomain', 'subdomain', 'testshop', [...], [...], 'subdomain')
 6 SYSTEMPATH/Controller.php(138): CodeIgniter\Validation\Validation->run()
 7 APPPATH/Controllers/Shop.php(122): CodeIgniter\Controller->validate([...], [...])
 8 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Shop->create()
 9 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Shop))
10 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-14 23:18:02 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'subdomain' in 'WHERE'
in SYSTEMPATH/Database/MySQLi/Connection.php on line 332.
 1 SYSTEMPATH/Database/BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1
FROM `shops`
WHERE `subdomain` = \'testshop\'
 LIMIT 1')
 2 SYSTEMPATH/Database/BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1
FROM `shops`
WHERE `subdomain` = \'testshop\'
 LIMIT 1')
 3 SYSTEMPATH/Database/BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `shops`
WHERE `subdomain` = :subdomain:
 LIMIT 1', [...], false)
 4 SYSTEMPATH/Validation/Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH/Validation/StrictRules/Rules.php(187): CodeIgniter\Validation\Rules->is_unique('testshop', 'shops.subdomain', [...])
 6 SYSTEMPATH/Validation/Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('testshop', 'shops.subdomain', [...], null, 'subdomain')
 7 SYSTEMPATH/Validation/Validation.php(201): CodeIgniter\Validation\Validation->processRules('subdomain', 'subdomain', 'testshop', [...], [...], 'subdomain')
 8 SYSTEMPATH/Controller.php(138): CodeIgniter\Validation\Validation->run()
 9 APPPATH/Controllers/Shop.php(122): CodeIgniter\Controller->validate([...], [...])
10 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Shop->create()
11 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Shop))
12 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
15 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-14 23:18:02 --> [Caused by] mysqli_sql_exception: Unknown column 'subdomain' in 'WHERE'
in SYSTEMPATH/Database/MySQLi/Connection.php on line 327.
 1 SYSTEMPATH/Database/MySQLi/Connection.php(327): mysqli->query('SELECT 1
FROM `shops`
WHERE `subdomain` = \'testshop\'
 LIMIT 1', 0)
 2 SYSTEMPATH/Database/BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1
FROM `shops`
WHERE `subdomain` = \'testshop\'
 LIMIT 1')
 3 SYSTEMPATH/Database/BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1
FROM `shops`
WHERE `subdomain` = \'testshop\'
 LIMIT 1')
 4 SYSTEMPATH/Database/BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `shops`
WHERE `subdomain` = :subdomain:
 LIMIT 1', [...], false)
 5 SYSTEMPATH/Validation/Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH/Validation/StrictRules/Rules.php(187): CodeIgniter\Validation\Rules->is_unique('testshop', 'shops.subdomain', [...])
 7 SYSTEMPATH/Validation/Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('testshop', 'shops.subdomain', [...], null, 'subdomain')
 8 SYSTEMPATH/Validation/Validation.php(201): CodeIgniter\Validation\Validation->processRules('subdomain', 'subdomain', 'testshop', [...], [...], 'subdomain')
 9 SYSTEMPATH/Controller.php(138): CodeIgniter\Validation\Validation->run()
10 APPPATH/Controllers/Shop.php(122): CodeIgniter\Controller->validate([...], [...])
11 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Shop->create()
12 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Shop))
13 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
14 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
15 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
16 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
ERROR - 2025-06-14 23:18:49 --> mysqli_sql_exception: Unknown column 'subdomain' in 'WHERE' in /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(327): mysqli->query('SELECT 1\nFROM `...', 0)
#1 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1\nFROM `...')
#2 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1\nFROM `...')
#3 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1\nFROM `...', Array, false)
#4 /var/www/html/vendor/codeigniter4/framework/system/Validation/Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
#5 /var/www/html/vendor/codeigniter4/framework/system/Validation/StrictRules/Rules.php(187): CodeIgniter\Validation\Rules->is_unique('testshop', 'shops.subdomain', Array)
#6 /var/www/html/vendor/codeigniter4/framework/system/Validation/Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('testshop', 'shops.subdomain', Array, NULL, 'subdomain')
#7 /var/www/html/vendor/codeigniter4/framework/system/Validation/Validation.php(201): CodeIgniter\Validation\Validation->processRules('subdomain', 'subdomain', 'testshop', Array, Array, 'subdomain')
#8 /var/www/html/vendor/codeigniter4/framework/system/Controller.php(138): CodeIgniter\Validation\Validation->run()
#9 /var/www/html/app/Controllers/Shop.php(122): CodeIgniter\Controller->validate(Array, Array)
#10 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(933): App\Controllers\Shop->create()
#11 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Shop))
#12 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#13 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(334): CodeIgniter\CodeIgniter->run()
#14 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#15 /var/www/html/public/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#16 {main}
CRITICAL - 2025-06-14 23:18:49 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'subdomain' in 'WHERE'
[Method: POST, Route: api/shops]
in SYSTEMPATH/Database/BaseConnection.php on line 692.
 1 SYSTEMPATH/Database/BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `shops`
WHERE `subdomain` = :subdomain:
 LIMIT 1', [...], false)
 2 SYSTEMPATH/Validation/Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH/Validation/StrictRules/Rules.php(187): CodeIgniter\Validation\Rules->is_unique('testshop', 'shops.subdomain', [...])
 4 SYSTEMPATH/Validation/Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('testshop', 'shops.subdomain', [...], null, 'subdomain')
 5 SYSTEMPATH/Validation/Validation.php(201): CodeIgniter\Validation\Validation->processRules('subdomain', 'subdomain', 'testshop', [...], [...], 'subdomain')
 6 SYSTEMPATH/Controller.php(138): CodeIgniter\Validation\Validation->run()
 7 APPPATH/Controllers/Shop.php(122): CodeIgniter\Controller->validate([...], [...])
 8 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Shop->create()
 9 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Shop))
10 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-14 23:18:49 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'subdomain' in 'WHERE'
in SYSTEMPATH/Database/MySQLi/Connection.php on line 332.
 1 SYSTEMPATH/Database/BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1
FROM `shops`
WHERE `subdomain` = \'testshop\'
 LIMIT 1')
 2 SYSTEMPATH/Database/BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1
FROM `shops`
WHERE `subdomain` = \'testshop\'
 LIMIT 1')
 3 SYSTEMPATH/Database/BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `shops`
WHERE `subdomain` = :subdomain:
 LIMIT 1', [...], false)
 4 SYSTEMPATH/Validation/Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH/Validation/StrictRules/Rules.php(187): CodeIgniter\Validation\Rules->is_unique('testshop', 'shops.subdomain', [...])
 6 SYSTEMPATH/Validation/Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('testshop', 'shops.subdomain', [...], null, 'subdomain')
 7 SYSTEMPATH/Validation/Validation.php(201): CodeIgniter\Validation\Validation->processRules('subdomain', 'subdomain', 'testshop', [...], [...], 'subdomain')
 8 SYSTEMPATH/Controller.php(138): CodeIgniter\Validation\Validation->run()
 9 APPPATH/Controllers/Shop.php(122): CodeIgniter\Controller->validate([...], [...])
10 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Shop->create()
11 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Shop))
12 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
15 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-14 23:18:49 --> [Caused by] mysqli_sql_exception: Unknown column 'subdomain' in 'WHERE'
in SYSTEMPATH/Database/MySQLi/Connection.php on line 327.
 1 SYSTEMPATH/Database/MySQLi/Connection.php(327): mysqli->query('SELECT 1
FROM `shops`
WHERE `subdomain` = \'testshop\'
 LIMIT 1', 0)
 2 SYSTEMPATH/Database/BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1
FROM `shops`
WHERE `subdomain` = \'testshop\'
 LIMIT 1')
 3 SYSTEMPATH/Database/BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1
FROM `shops`
WHERE `subdomain` = \'testshop\'
 LIMIT 1')
 4 SYSTEMPATH/Database/BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `shops`
WHERE `subdomain` = :subdomain:
 LIMIT 1', [...], false)
 5 SYSTEMPATH/Validation/Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH/Validation/StrictRules/Rules.php(187): CodeIgniter\Validation\Rules->is_unique('testshop', 'shops.subdomain', [...])
 7 SYSTEMPATH/Validation/Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('testshop', 'shops.subdomain', [...], null, 'subdomain')
 8 SYSTEMPATH/Validation/Validation.php(201): CodeIgniter\Validation\Validation->processRules('subdomain', 'subdomain', 'testshop', [...], [...], 'subdomain')
 9 SYSTEMPATH/Controller.php(138): CodeIgniter\Validation\Validation->run()
10 APPPATH/Controllers/Shop.php(122): CodeIgniter\Controller->validate([...], [...])
11 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Shop->create()
12 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Shop))
13 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
14 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
15 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
16 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
ERROR - 2025-06-14 23:18:59 --> mysqli_sql_exception: Unknown column 'subdomain' in 'WHERE' in /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(327): mysqli->query('SELECT 1\nFROM `...', 0)
#1 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1\nFROM `...')
#2 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1\nFROM `...')
#3 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1\nFROM `...', Array, false)
#4 /var/www/html/vendor/codeigniter4/framework/system/Validation/Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
#5 /var/www/html/vendor/codeigniter4/framework/system/Validation/StrictRules/Rules.php(187): CodeIgniter\Validation\Rules->is_unique('testshop', 'shops.subdomain', Array)
#6 /var/www/html/vendor/codeigniter4/framework/system/Validation/Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('testshop', 'shops.subdomain', Array, NULL, 'subdomain')
#7 /var/www/html/vendor/codeigniter4/framework/system/Validation/Validation.php(201): CodeIgniter\Validation\Validation->processRules('subdomain', 'subdomain', 'testshop', Array, Array, 'subdomain')
#8 /var/www/html/vendor/codeigniter4/framework/system/Controller.php(138): CodeIgniter\Validation\Validation->run()
#9 /var/www/html/app/Controllers/Shop.php(122): CodeIgniter\Controller->validate(Array, Array)
#10 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(933): App\Controllers\Shop->create()
#11 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Shop))
#12 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#13 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(334): CodeIgniter\CodeIgniter->run()
#14 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#15 /var/www/html/public/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#16 {main}
CRITICAL - 2025-06-14 23:18:59 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'subdomain' in 'WHERE'
[Method: POST, Route: api/shops]
in SYSTEMPATH/Database/BaseConnection.php on line 692.
 1 SYSTEMPATH/Database/BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `shops`
WHERE `subdomain` = :subdomain:
 LIMIT 1', [...], false)
 2 SYSTEMPATH/Validation/Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH/Validation/StrictRules/Rules.php(187): CodeIgniter\Validation\Rules->is_unique('testshop', 'shops.subdomain', [...])
 4 SYSTEMPATH/Validation/Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('testshop', 'shops.subdomain', [...], null, 'subdomain')
 5 SYSTEMPATH/Validation/Validation.php(201): CodeIgniter\Validation\Validation->processRules('subdomain', 'subdomain', 'testshop', [...], [...], 'subdomain')
 6 SYSTEMPATH/Controller.php(138): CodeIgniter\Validation\Validation->run()
 7 APPPATH/Controllers/Shop.php(122): CodeIgniter\Controller->validate([...], [...])
 8 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Shop->create()
 9 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Shop))
10 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-14 23:18:59 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'subdomain' in 'WHERE'
in SYSTEMPATH/Database/MySQLi/Connection.php on line 332.
 1 SYSTEMPATH/Database/BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1
FROM `shops`
WHERE `subdomain` = \'testshop\'
 LIMIT 1')
 2 SYSTEMPATH/Database/BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1
FROM `shops`
WHERE `subdomain` = \'testshop\'
 LIMIT 1')
 3 SYSTEMPATH/Database/BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `shops`
WHERE `subdomain` = :subdomain:
 LIMIT 1', [...], false)
 4 SYSTEMPATH/Validation/Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH/Validation/StrictRules/Rules.php(187): CodeIgniter\Validation\Rules->is_unique('testshop', 'shops.subdomain', [...])
 6 SYSTEMPATH/Validation/Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('testshop', 'shops.subdomain', [...], null, 'subdomain')
 7 SYSTEMPATH/Validation/Validation.php(201): CodeIgniter\Validation\Validation->processRules('subdomain', 'subdomain', 'testshop', [...], [...], 'subdomain')
 8 SYSTEMPATH/Controller.php(138): CodeIgniter\Validation\Validation->run()
 9 APPPATH/Controllers/Shop.php(122): CodeIgniter\Controller->validate([...], [...])
10 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Shop->create()
11 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Shop))
12 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
15 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-14 23:18:59 --> [Caused by] mysqli_sql_exception: Unknown column 'subdomain' in 'WHERE'
in SYSTEMPATH/Database/MySQLi/Connection.php on line 327.
 1 SYSTEMPATH/Database/MySQLi/Connection.php(327): mysqli->query('SELECT 1
FROM `shops`
WHERE `subdomain` = \'testshop\'
 LIMIT 1', 0)
 2 SYSTEMPATH/Database/BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1
FROM `shops`
WHERE `subdomain` = \'testshop\'
 LIMIT 1')
 3 SYSTEMPATH/Database/BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1
FROM `shops`
WHERE `subdomain` = \'testshop\'
 LIMIT 1')
 4 SYSTEMPATH/Database/BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `shops`
WHERE `subdomain` = :subdomain:
 LIMIT 1', [...], false)
 5 SYSTEMPATH/Validation/Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH/Validation/StrictRules/Rules.php(187): CodeIgniter\Validation\Rules->is_unique('testshop', 'shops.subdomain', [...])
 7 SYSTEMPATH/Validation/Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('testshop', 'shops.subdomain', [...], null, 'subdomain')
 8 SYSTEMPATH/Validation/Validation.php(201): CodeIgniter\Validation\Validation->processRules('subdomain', 'subdomain', 'testshop', [...], [...], 'subdomain')
 9 SYSTEMPATH/Controller.php(138): CodeIgniter\Validation\Validation->run()
10 APPPATH/Controllers/Shop.php(122): CodeIgniter\Controller->validate([...], [...])
11 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Shop->create()
12 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Shop))
13 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
14 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
15 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
16 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
ERROR - 2025-06-14 23:19:57 --> mysqli_sql_exception: Duplicate entry '' for key 'shop_domain' in /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(327): mysqli->query('INSERT INTO `sh...', 0)
#1 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `sh...')
#2 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `sh...')
#3 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `sh...', Array, false)
#4 /var/www/html/vendor/codeigniter4/framework/system/Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 /var/www/html/vendor/codeigniter4/framework/system/BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 /var/www/html/vendor/codeigniter4/framework/system/Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 /var/www/html/app/Controllers/Shop.php(131): CodeIgniter\Model->insert(Array)
#8 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(933): App\Controllers\Shop->create()
#9 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Shop))
#10 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 /var/www/html/public/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 {main}
CRITICAL - 2025-06-14 23:20:45 --> TypeError: CodeIgniter\DataCaster\Cast\JsonCast::get(): Return value must be of type stdClass|array, string returned
[Method: POST, Route: api/shops]
in SYSTEMPATH/DataCaster/Cast/JsonCast.php on line 47.
 1 SYSTEMPATH/DataCaster/DataCaster.php(186): CodeIgniter\DataCaster\Cast\JsonCast::get('"[]"', [], Object(CodeIgniter\Database\MySQLi\Connection))
 2 SYSTEMPATH/DataConverter/DataConverter.php(80): CodeIgniter\DataCaster\DataCaster->castAs('"[]"', 'settings', 'get')
 3 SYSTEMPATH/BaseModel.php(1945): CodeIgniter\DataConverter\DataConverter->fromDataSource([...])
 4 SYSTEMPATH/Model.php(224): CodeIgniter\BaseModel->convertToReturnType([...], 'array')
 5 SYSTEMPATH/BaseModel.php(613): CodeIgniter\Model->doFind(true, 4)
 6 APPPATH/Controllers/Shop.php(140): CodeIgniter\BaseModel->find(4)
 7 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Shop->create()
 8 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Shop))
 9 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
