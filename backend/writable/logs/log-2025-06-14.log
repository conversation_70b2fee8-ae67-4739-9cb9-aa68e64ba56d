CRITICAL - 2025-06-14 21:30:52 --> Error: Class "Locale" not found
[Method: GET, Route: /]
in SYSTEMPATH/CodeIgniter.php on line 189.
 1 SYSTEMPATH/Boot.php(321): CodeIgniter\CodeIgniter->initialize()
 2 SYSTEMPATH/Boot.php(66): CodeIgniter\Boot::initializeCodeIgniter()
 3 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-14 21:30:52 --> ErrorException: Uncaught Error: Class "Locale" not found in /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Helpers/number_helper.php:142
Stack trace:
#0 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Helpers/number_helper.php(60): format_number(6.0, 2, NULL, Array)
#1 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/BaseExceptionHandler.php(154): number_to_size(6.0, 2)
#2 /var/www/shopci.test/backend/app/Views/errors/html/error_exception.php(408): CodeIgniter\Debug\BaseExceptionHandler::describeMemory(6291456)
#3 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/BaseExceptionHandler.php(266): include('/var/www/shopci...')
#4 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/BaseExceptionHandler.php(269): CodeIgniter\Debug\BaseExceptionHandler->{closure:CodeIgniter\Debug\BaseExceptionHandler::render():260}()
#5 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/ExceptionHandler.php(116): CodeIgniter\Debug\BaseExceptionHandler->render(Object(Error), 500, '/var/www/shopci...')
#6 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/Exceptions.php(162): CodeIgniter\Debug\ExceptionHandler->handle(Object(Error), Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), 500, 1)
#7 [internal function]: CodeIgniter\Debug\Exceptions->exceptionHandler(Object(Error))
#8 {main}
  thrown
【Previous Exception】
Error
Class "Locale" not found
#0 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Boot.php(321): CodeIgniter\CodeIgniter->initialize()
#1 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Boot.php(66): CodeIgniter\Boot::initializeCodeIgniter()
#2 /var/www/shopci.test/backend/public/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#3 {main}
[Method: GET, Route: /]
in SYSTEMPATH/Helpers/number_helper.php on line 142.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
ERROR - 2025-06-14 21:33:05 --> Error connecting to the database: mysqli_sql_exception: No such file or directory in /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:201
Stack trace:
#0 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'shopci_platform', 3306, '', 0)
#1 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/BaseConnection.php(1525): CodeIgniter\Database\BaseConnection->query('SHOW TABLES FRO...')
#4 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/BaseConnection.php(1545): CodeIgniter\Database\BaseConnection->listTables()
#5 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/MigrationRunner.php(768): CodeIgniter\Database\BaseConnection->tableExists('migrations')
#6 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/MigrationRunner.php(162): CodeIgniter\Database\MigrationRunner->ensureTable()
#7 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Commands/Database/Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/CLI/Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/CLI/Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Boot.php(360): CodeIgniter\CLI\Console->run()
#11 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 /Users/<USER>/Webdevbox/web/shopci.test/backend/spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: No such file or directory in /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:246
Stack trace:
#0 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/BaseConnection.php(1525): CodeIgniter\Database\BaseConnection->query('SHOW TABLES FRO...')
#3 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/BaseConnection.php(1545): CodeIgniter\Database\BaseConnection->listTables()
#4 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/MigrationRunner.php(768): CodeIgniter\Database\BaseConnection->tableExists('migrations')
#5 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/MigrationRunner.php(162): CodeIgniter\Database\MigrationRunner->ensureTable()
#6 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Commands/Database/Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#7 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/CLI/Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#8 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/CLI/Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#9 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Boot.php(360): CodeIgniter\CLI\Console->run()
#10 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#11 /Users/<USER>/Webdevbox/web/shopci.test/backend/spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#12 {main}
CRITICAL - 2025-06-14 21:35:09 --> Error: Class "Locale" not found
[Method: GET, Route: /]
in SYSTEMPATH/CodeIgniter.php on line 189.
 1 SYSTEMPATH/Boot.php(321): CodeIgniter\CodeIgniter->initialize()
 2 SYSTEMPATH/Boot.php(66): CodeIgniter\Boot::initializeCodeIgniter()
 3 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-14 21:35:09 --> ErrorException: Uncaught Error: Class "Locale" not found in /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Helpers/number_helper.php:142
Stack trace:
#0 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Helpers/number_helper.php(60): format_number(6.0, 2, NULL, Array)
#1 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/BaseExceptionHandler.php(154): number_to_size(6.0, 2)
#2 /var/www/shopci.test/backend/app/Views/errors/html/error_exception.php(408): CodeIgniter\Debug\BaseExceptionHandler::describeMemory(6291456)
#3 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/BaseExceptionHandler.php(266): include('/var/www/shopci...')
#4 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/BaseExceptionHandler.php(269): CodeIgniter\Debug\BaseExceptionHandler->{closure:CodeIgniter\Debug\BaseExceptionHandler::render():260}()
#5 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/ExceptionHandler.php(116): CodeIgniter\Debug\BaseExceptionHandler->render(Object(Error), 500, '/var/www/shopci...')
#6 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/Exceptions.php(162): CodeIgniter\Debug\ExceptionHandler->handle(Object(Error), Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), 500, 1)
#7 [internal function]: CodeIgniter\Debug\Exceptions->exceptionHandler(Object(Error))
#8 {main}
  thrown
【Previous Exception】
Error
Class "Locale" not found
#0 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Boot.php(321): CodeIgniter\CodeIgniter->initialize()
#1 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Boot.php(66): CodeIgniter\Boot::initializeCodeIgniter()
#2 /var/www/shopci.test/backend/public/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#3 {main}
[Method: GET, Route: /]
in SYSTEMPATH/Helpers/number_helper.php on line 142.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
CRITICAL - 2025-06-14 21:38:26 --> Error: Class "Locale" not found
[Method: GET, Route: /]
in SYSTEMPATH/CodeIgniter.php on line 189.
 1 SYSTEMPATH/Boot.php(321): CodeIgniter\CodeIgniter->initialize()
 2 SYSTEMPATH/Boot.php(66): CodeIgniter\Boot::initializeCodeIgniter()
 3 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-14 21:38:27 --> ErrorException: Uncaught Error: Class "Locale" not found in /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Helpers/number_helper.php:142
Stack trace:
#0 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Helpers/number_helper.php(60): format_number(8.0, 2, NULL, Array)
#1 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/BaseExceptionHandler.php(154): number_to_size(8.0, 2)
#2 /var/www/shopci.test/backend/app/Views/errors/html/error_exception.php(408): CodeIgniter\Debug\BaseExceptionHandler::describeMemory(8388608)
#3 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/BaseExceptionHandler.php(266): include('/var/www/shopci...')
#4 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/BaseExceptionHandler.php(269): CodeIgniter\Debug\BaseExceptionHandler->{closure:CodeIgniter\Debug\BaseExceptionHandler::render():260}()
#5 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/ExceptionHandler.php(116): CodeIgniter\Debug\BaseExceptionHandler->render(Object(Error), 500, '/var/www/shopci...')
#6 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/Exceptions.php(162): CodeIgniter\Debug\ExceptionHandler->handle(Object(Error), Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), 500, 1)
#7 [internal function]: CodeIgniter\Debug\Exceptions->exceptionHandler(Object(Error))
#8 {main}
  thrown
【Previous Exception】
Error
Class "Locale" not found
#0 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Boot.php(321): CodeIgniter\CodeIgniter->initialize()
#1 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Boot.php(66): CodeIgniter\Boot::initializeCodeIgniter()
#2 /var/www/shopci.test/backend/public/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#3 {main}
[Method: GET, Route: /]
in SYSTEMPATH/Helpers/number_helper.php on line 142.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
CRITICAL - 2025-06-14 21:38:39 --> Error: Class "Locale" not found
[Method: GET, Route: admin]
in SYSTEMPATH/CodeIgniter.php on line 189.
 1 SYSTEMPATH/Boot.php(321): CodeIgniter\CodeIgniter->initialize()
 2 SYSTEMPATH/Boot.php(66): CodeIgniter\Boot::initializeCodeIgniter()
 3 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-14 21:38:40 --> ErrorException: Uncaught Error: Class "Locale" not found in /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Helpers/number_helper.php:142
Stack trace:
#0 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Helpers/number_helper.php(60): format_number(6.0, 2, NULL, Array)
#1 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/BaseExceptionHandler.php(154): number_to_size(6.0, 2)
#2 /var/www/shopci.test/backend/app/Views/errors/html/error_exception.php(408): CodeIgniter\Debug\BaseExceptionHandler::describeMemory(6291456)
#3 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/BaseExceptionHandler.php(266): include('/var/www/shopci...')
#4 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/BaseExceptionHandler.php(269): CodeIgniter\Debug\BaseExceptionHandler->{closure:CodeIgniter\Debug\BaseExceptionHandler::render():260}()
#5 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/ExceptionHandler.php(116): CodeIgniter\Debug\BaseExceptionHandler->render(Object(Error), 500, '/var/www/shopci...')
#6 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/Exceptions.php(162): CodeIgniter\Debug\ExceptionHandler->handle(Object(Error), Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), 500, 1)
#7 [internal function]: CodeIgniter\Debug\Exceptions->exceptionHandler(Object(Error))
#8 {main}
  thrown
【Previous Exception】
Error
Class "Locale" not found
#0 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Boot.php(321): CodeIgniter\CodeIgniter->initialize()
#1 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Boot.php(66): CodeIgniter\Boot::initializeCodeIgniter()
#2 /var/www/shopci.test/backend/public/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#3 {main}
[Method: GET, Route: admin]
in SYSTEMPATH/Helpers/number_helper.php on line 142.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
CRITICAL - 2025-06-14 21:38:44 --> Error: Class "Locale" not found
[Method: GET, Route: /]
in SYSTEMPATH/CodeIgniter.php on line 189.
 1 SYSTEMPATH/Boot.php(321): CodeIgniter\CodeIgniter->initialize()
 2 SYSTEMPATH/Boot.php(66): CodeIgniter\Boot::initializeCodeIgniter()
 3 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-14 21:38:44 --> ErrorException: Uncaught Error: Class "Locale" not found in /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Helpers/number_helper.php:142
Stack trace:
#0 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Helpers/number_helper.php(60): format_number(6.0, 2, NULL, Array)
#1 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/BaseExceptionHandler.php(154): number_to_size(6.0, 2)
#2 /var/www/shopci.test/backend/app/Views/errors/html/error_exception.php(408): CodeIgniter\Debug\BaseExceptionHandler::describeMemory(6291456)
#3 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/BaseExceptionHandler.php(266): include('/var/www/shopci...')
#4 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/BaseExceptionHandler.php(269): CodeIgniter\Debug\BaseExceptionHandler->{closure:CodeIgniter\Debug\BaseExceptionHandler::render():260}()
#5 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/ExceptionHandler.php(116): CodeIgniter\Debug\BaseExceptionHandler->render(Object(Error), 500, '/var/www/shopci...')
#6 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/Exceptions.php(162): CodeIgniter\Debug\ExceptionHandler->handle(Object(Error), Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), 500, 1)
#7 [internal function]: CodeIgniter\Debug\Exceptions->exceptionHandler(Object(Error))
#8 {main}
  thrown
【Previous Exception】
Error
Class "Locale" not found
#0 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Boot.php(321): CodeIgniter\CodeIgniter->initialize()
#1 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Boot.php(66): CodeIgniter\Boot::initializeCodeIgniter()
#2 /var/www/shopci.test/backend/public/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#3 {main}
[Method: GET, Route: /]
in SYSTEMPATH/Helpers/number_helper.php on line 142.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
CRITICAL - 2025-06-14 21:39:26 --> Error: Class "Locale" not found
[Method: GET, Route: admin]
in SYSTEMPATH/CodeIgniter.php on line 189.
 1 SYSTEMPATH/Boot.php(321): CodeIgniter\CodeIgniter->initialize()
 2 SYSTEMPATH/Boot.php(66): CodeIgniter\Boot::initializeCodeIgniter()
 3 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-14 21:39:26 --> ErrorException: Uncaught Error: Class "Locale" not found in /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Helpers/number_helper.php:142
Stack trace:
#0 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Helpers/number_helper.php(60): format_number(6.0, 2, NULL, Array)
#1 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/BaseExceptionHandler.php(154): number_to_size(6.0, 2)
#2 /var/www/shopci.test/backend/app/Views/errors/html/error_exception.php(408): CodeIgniter\Debug\BaseExceptionHandler::describeMemory(6291456)
#3 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/BaseExceptionHandler.php(266): include('/var/www/shopci...')
#4 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/BaseExceptionHandler.php(269): CodeIgniter\Debug\BaseExceptionHandler->{closure:CodeIgniter\Debug\BaseExceptionHandler::render():260}()
#5 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/ExceptionHandler.php(116): CodeIgniter\Debug\BaseExceptionHandler->render(Object(Error), 500, '/var/www/shopci...')
#6 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Debug/Exceptions.php(162): CodeIgniter\Debug\ExceptionHandler->handle(Object(Error), Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), 500, 1)
#7 [internal function]: CodeIgniter\Debug\Exceptions->exceptionHandler(Object(Error))
#8 {main}
  thrown
【Previous Exception】
Error
Class "Locale" not found
#0 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Boot.php(321): CodeIgniter\CodeIgniter->initialize()
#1 /var/www/shopci.test/backend/vendor/codeigniter4/framework/system/Boot.php(66): CodeIgniter\Boot::initializeCodeIgniter()
#2 /var/www/shopci.test/backend/public/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#3 {main}
[Method: GET, Route: admin]
in SYSTEMPATH/Helpers/number_helper.php on line 142.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
ERROR - 2025-06-14 21:39:47 --> Error connecting to the database: ErrorException: mysqli::real_connect(): php_network_getaddresses: getaddrinfo for mariadb failed: nodename nor servname provided, or not known in /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:201
Stack trace:
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'mysqli::real_co...', '/Users/<USER>/Web...', 201)
#1 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(201): mysqli->real_connect('mariadb', 'root', Object(SensitiveParameterValue), 'shopci_platform', 3306, '', 0)
#2 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#3 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#4 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/BaseConnection.php(1525): CodeIgniter\Database\BaseConnection->query('SHOW TABLES FRO...')
#5 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/BaseConnection.php(1545): CodeIgniter\Database\BaseConnection->listTables()
#6 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/MigrationRunner.php(768): CodeIgniter\Database\BaseConnection->tableExists('migrations')
#7 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/MigrationRunner.php(162): CodeIgniter\Database\MigrationRunner->ensureTable()
#8 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Commands/Database/Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#9 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/CLI/Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#10 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/CLI/Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#11 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Boot.php(360): CodeIgniter\CLI\Console->run()
#12 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#13 /Users/<USER>/Webdevbox/web/shopci.test/backend/spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#14 {main}

Next mysqli_sql_exception: php_network_getaddresses: getaddrinfo for mariadb failed: nodename nor servname provided, or not known in /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:201
Stack trace:
#0 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(201): mysqli->real_connect('mariadb', 'root', Object(SensitiveParameterValue), 'shopci_platform', 3306, '', 0)
#1 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/BaseConnection.php(1525): CodeIgniter\Database\BaseConnection->query('SHOW TABLES FRO...')
#4 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/BaseConnection.php(1545): CodeIgniter\Database\BaseConnection->listTables()
#5 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/MigrationRunner.php(768): CodeIgniter\Database\BaseConnection->tableExists('migrations')
#6 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/MigrationRunner.php(162): CodeIgniter\Database\MigrationRunner->ensureTable()
#7 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Commands/Database/Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/CLI/Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/CLI/Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Boot.php(360): CodeIgniter\CLI\Console->run()
#11 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 /Users/<USER>/Webdevbox/web/shopci.test/backend/spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: php_network_getaddresses: getaddrinfo for mariadb failed: nodename nor servname provided, or not known in /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:246
Stack trace:
#0 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/BaseConnection.php(1525): CodeIgniter\Database\BaseConnection->query('SHOW TABLES FRO...')
#3 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/BaseConnection.php(1545): CodeIgniter\Database\BaseConnection->listTables()
#4 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/MigrationRunner.php(768): CodeIgniter\Database\BaseConnection->tableExists('migrations')
#5 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Database/MigrationRunner.php(162): CodeIgniter\Database\MigrationRunner->ensureTable()
#6 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Commands/Database/Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#7 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/CLI/Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#8 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/CLI/Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#9 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Boot.php(360): CodeIgniter\CLI\Console->run()
#10 /Users/<USER>/Webdevbox/web/shopci.test/backend/vendor/codeigniter4/framework/system/Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#11 /Users/<USER>/Webdevbox/web/shopci.test/backend/spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#12 {main}
ERROR - 2025-06-14 22:10:02 --> mysqli_sql_exception: Table 'platform_users' already exists in /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(327): mysqli->query('CREATE TABLE `p...', 0)
#1 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `p...')
#2 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `p...')
#3 /var/www/html/vendor/codeigniter4/framework/system/Database/Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `p...')
#4 /var/www/html/app/Database/Migrations/2025-06-14-212742_CreatePlatformUsersTable.php(71): CodeIgniter\Database\Forge->createTable('platform_users')
#5 /var/www/html/vendor/codeigniter4/framework/system/Database/MigrationRunner.php(876): App\Database\Migrations\CreatePlatformUsersTable->up()
#6 /var/www/html/vendor/codeigniter4/framework/system/Database/MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 /var/www/html/vendor/codeigniter4/framework/system/Commands/Database/Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 /var/www/html/vendor/codeigniter4/framework/system/CLI/Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 /var/www/html/vendor/codeigniter4/framework/system/CLI/Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(360): CodeIgniter\CLI\Console->run()
#11 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 /var/www/html/spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}
