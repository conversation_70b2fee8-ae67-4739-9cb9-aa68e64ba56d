CRITICAL - 2025-06-14 22:52:34 --> CodeIgniter\Exceptions\InvalidArgumentException: [CodeIgniter\DataCaster\Cast\DatetimeCast] Invalid value type: string, and its value: '2025-06-14 22:52:34'
[Method: POST, Route: api/auth/login]
in SYSTEMPATH/DataCaster/Cast/BaseCast.php on line 43.
 1 SYSTEMPATH/DataCaster/Cast/DatetimeCast.php(57): CodeIgniter\DataCaster\Cast\BaseCast::invalidTypeValueError('2025-06-14 22:52:34')
 2 SYSTEMPATH/DataCaster/DataCaster.php(186): CodeIgniter\DataCaster\Cast\DatetimeCast::set('2025-06-14 22:52:34', [], Object(CodeIgniter\Database\MySQLi\Connection))
 3 SYSTEMPATH/DataConverter/DataConverter.php(98): CodeIgniter\DataCaster\DataCaster->castAs('2025-06-14 22:52:34', 'last_login_at', 'set')
 4 SYSTEMPATH/BaseModel.php(1846): CodeIgniter\DataConverter\DataConverter->toDataSource([...])
 5 SYSTEMPATH/BaseModel.php(1000): CodeIgniter\BaseModel->transformDataToArray([...], 'update')
 6 SYSTEMPATH/Model.php(863): CodeIgniter\BaseModel->update([...], [...])
 7 APPPATH/Controllers/Auth.php(165): CodeIgniter\Model->update('1', [...])
 8 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Auth->login()
 9 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
10 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-14 22:56:43 --> CodeIgniter\Filters\Exceptions\FilterException: "toolbar" filter must have a matching alias defined.
[Method: GET, Route: admin/login]
in SYSTEMPATH/Filters/Filters.php on line 402.
 1 SYSTEMPATH/Filters/Filters.php(402): CodeIgniter\Filters\Exceptions\FilterException::forNoAlias('toolbar')
 2 SYSTEMPATH/Filters/Filters.php(319): CodeIgniter\Filters\Filters->getRequiredFilters('after')
 3 SYSTEMPATH/Filters/Filters.php(353): CodeIgniter\Filters\Filters->getRequiredClasses('after')
 4 SYSTEMPATH/CodeIgniter.php(407): CodeIgniter\Filters\Filters->runRequired('after')
 5 SYSTEMPATH/CodeIgniter.php(368): CodeIgniter\CodeIgniter->runRequiredAfterFilters(Object(CodeIgniter\Filters\Filters))
 6 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 8 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-14 23:09:08 --> CodeIgniter\Database\Exceptions\DataException: There is no data to update.
[Method: POST, Route: api/auth/reset-password]
in SYSTEMPATH/BaseModel.php on line 1014.
 1 SYSTEMPATH/BaseModel.php(1014): CodeIgniter\Database\Exceptions\DataException::forEmptyDataset('update')
 2 SYSTEMPATH/Model.php(863): CodeIgniter\BaseModel->update([...], [])
 3 APPPATH/Controllers/Auth.php(311): CodeIgniter\Model->update('1', [...])
 4 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Auth->resetPassword()
 5 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 6 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-14 23:09:39 --> CodeIgniter\Exceptions\InvalidArgumentException: [CodeIgniter\DataCaster\Cast\DatetimeCast] Invalid value type: string, and its value: '2025-06-14 23:09:39'
[Method: POST, Route: api/auth/login]
in SYSTEMPATH/DataCaster/Cast/BaseCast.php on line 43.
 1 SYSTEMPATH/DataCaster/Cast/DatetimeCast.php(57): CodeIgniter\DataCaster\Cast\BaseCast::invalidTypeValueError('2025-06-14 23:09:39')
 2 SYSTEMPATH/DataCaster/DataCaster.php(186): CodeIgniter\DataCaster\Cast\DatetimeCast::set('2025-06-14 23:09:39', [], Object(CodeIgniter\Database\MySQLi\Connection))
 3 SYSTEMPATH/DataConverter/DataConverter.php(98): CodeIgniter\DataCaster\DataCaster->castAs('2025-06-14 23:09:39', 'last_login_at', 'set')
 4 SYSTEMPATH/BaseModel.php(1846): CodeIgniter\DataConverter\DataConverter->toDataSource([...])
 5 SYSTEMPATH/BaseModel.php(1000): CodeIgniter\BaseModel->transformDataToArray([...], 'update')
 6 SYSTEMPATH/Model.php(863): CodeIgniter\BaseModel->update([...], [...])
 7 APPPATH/Controllers/Auth.php(165): CodeIgniter\Model->update('1', [...])
 8 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Auth->login()
 9 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
10 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-14 23:10:12 --> CodeIgniter\Exceptions\InvalidArgumentException: [CodeIgniter\DataCaster\Cast\DatetimeCast] Invalid value type: DateTime
[Method: POST, Route: api/auth/login]
in SYSTEMPATH/DataCaster/Cast/BaseCast.php on line 43.
 1 SYSTEMPATH/DataCaster/Cast/DatetimeCast.php(57): CodeIgniter\DataCaster\Cast\BaseCast::invalidTypeValueError(Object(DateTime))
 2 SYSTEMPATH/DataCaster/DataCaster.php(186): CodeIgniter\DataCaster\Cast\DatetimeCast::set(Object(DateTime), [], Object(CodeIgniter\Database\MySQLi\Connection))
 3 SYSTEMPATH/DataConverter/DataConverter.php(98): CodeIgniter\DataCaster\DataCaster->castAs(Object(DateTime), 'last_login_at', 'set')
 4 SYSTEMPATH/BaseModel.php(1846): CodeIgniter\DataConverter\DataConverter->toDataSource([...])
 5 SYSTEMPATH/BaseModel.php(1000): CodeIgniter\BaseModel->transformDataToArray([...], 'update')
 6 SYSTEMPATH/Model.php(863): CodeIgniter\BaseModel->update([...], [...])
 7 APPPATH/Controllers/Auth.php(165): CodeIgniter\Model->update('1', [...])
 8 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Auth->login()
 9 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
10 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH/index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
