{"url": "http://shopci.test/index.php/health", "method": "GET", "isAJAX": false, "startTime": **********.876997, "totalTime": 523.8000000000001, "totalMemory": "5.629", "segmentDuration": 75, "segmentCount": 7, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.02403, "duration": 0.*****************}, {"name": "Required Before Filters", "component": "Timer", "start": **********.240154, "duration": 0.*****************}, {"name": "Routing", "component": "Timer", "start": **********.282577, "duration": 0.017606019973754883}, {"name": "Before Filters", "component": "Timer", "start": **********.303151, "duration": 1.2159347534179688e-05}, {"name": "Controller", "component": "Timer", "start": **********.303164, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.303165, "duration": 0.017374038696289062}, {"name": "After Filters", "component": "Timer", "start": **********.396741, "duration": 3.0994415283203125e-06}, {"name": "Required After Filters", "component": "Timer", "start": **********.396756, "duration": 0.*****************}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(1 total Query, 1  unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "2.66 ms", "sql": "<strong>SELECT</strong> 1", "trace": [{"file": "APPPATH/Controllers/Health.php:29", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Health->index()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  4    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  6    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  7    "}], "trace-file": "APPPATH/Controllers/Health.php:29", "qid": "a7f2799ec4b14bc38f947ab1df639079"}]}, "badgeValue": 1, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.336253, "duration": "0.003143"}, {"name": "Query", "component": "Database", "start": **********.343509, "duration": "0.002663", "query": "<strong>SELECT</strong> 1"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": []}, "badgeValue": null, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 0, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": []}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 154 )", "display": {"coreFiles": [{"path": "SYSTEMPATH/API/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/Autoloader/Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH/Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH/Cache/CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH/Cache/CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH/Cache/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Cache/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Cache/ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH/CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH/Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH/Config/AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH/Config/BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH/Config/BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH/Config/DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH/Config/Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH/Config/Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH/Config/Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH/Config/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH/Cookie/CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH/Cookie/CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH/Database/BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH/Database/BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH/Database/Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH/Database/ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH/Database/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH/Database/Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH/Database/QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH/Database/ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH/Debug/Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH/Debug/Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH/Debug/Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH/Events/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Filters/DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH/Filters/FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH/Filters/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Filters/ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH/Filters/PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH/Filters/PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH/Format/Format.php", "name": "Format.php"}, {"path": "SYSTEMPATH/Format/FormatterInterface.php", "name": "FormatterInterface.php"}, {"path": "SYSTEMPATH/Format/JSONFormatter.php", "name": "JSONFormatter.php"}, {"path": "SYSTEMPATH/HTTP/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH/HTTP/Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH/HTTP/IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH/HTTP/Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH/HTTP/MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH/HTTP/MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH/HTTP/Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH/HTTP/RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH/HTTP/Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH/HTTP/ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH/HTTP/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/HTTP/SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH/HTTP/SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH/HTTP/URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH/HTTP/UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH/Helpers/array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH/Helpers/filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH/Helpers/kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH/Helpers/url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH/I18n/Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH/I18n/TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH/Log/Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH/Modules/Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH/Router/RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH/Router/RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH/Router/Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH/Router/RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH/Session/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Session/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Session/Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH/Session/SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH/Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init.php", "name": "init.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH/Validation/FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH/View/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/View/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/View/ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH/Common.php", "name": "Common.php"}, {"path": "APPPATH/Config/App.php", "name": "App.php"}, {"path": "APPPATH/Config/Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH/Config/Boot/development.php", "name": "development.php"}, {"path": "APPPATH/Config/Cache.php", "name": "Cache.php"}, {"path": "APPPATH/Config/Constants.php", "name": "Constants.php"}, {"path": "APPPATH/Config/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH/Config/Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH/Config/Database.php", "name": "Database.php"}, {"path": "APPPATH/Config/Events.php", "name": "Events.php"}, {"path": "APPPATH/Config/Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH/Config/Feature.php", "name": "Feature.php"}, {"path": "APPPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "APPPATH/Config/Format.php", "name": "Format.php"}, {"path": "APPPATH/Config/Kint.php", "name": "Kint.php"}, {"path": "APPPATH/Config/Logger.php", "name": "Logger.php"}, {"path": "APPPATH/Config/Modules.php", "name": "Modules.php"}, {"path": "APPPATH/Config/Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH/Config/Paths.php", "name": "Paths.php"}, {"path": "APPPATH/Config/Routes.php", "name": "Routes.php"}, {"path": "APPPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "APPPATH/Config/Services.php", "name": "Services.php"}, {"path": "APPPATH/Config/Session.php", "name": "Session.php"}, {"path": "APPPATH/Config/Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH/Config/UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH/Config/View.php", "name": "View.php"}, {"path": "APPPATH/Controllers/BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH/Controllers/Health.php", "name": "Health.php"}, {"path": "FCPATH/index.php", "name": "index.php"}, {"path": "VENDORPATH/autoload.php", "name": "autoload.php"}, {"path": "VENDORPATH/composer/ClassLoader.php", "name": "ClassLoader.php"}, {"path": "VENDORPATH/composer/InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "VENDORPATH/composer/autoload_real.php", "name": "autoload_real.php"}, {"path": "VENDORPATH/composer/autoload_static.php", "name": "autoload_static.php"}, {"path": "VENDORPATH/composer/installed.php", "name": "installed.php"}, {"path": "VENDORPATH/composer/platform_check.php", "name": "platform_check.php"}, {"path": "VENDORPATH/laminas/laminas-escaper/src/Escaper.php", "name": "Escaper.php"}, {"path": "VENDORPATH/laminas/laminas-escaper/src/EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "VENDORPATH/myclabs/deep-copy/src/DeepCopy/deep_copy.php", "name": "deep_copy.php"}, {"path": "VENDORPATH/phpunit/phpunit/src/Framework/Assert/Functions.php", "name": "Functions.php"}, {"path": "VENDORPATH/psr/log/src/LogLevel.php", "name": "LogLevel.php"}, {"path": "VENDORPATH/psr/log/src/LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "VENDORPATH/psr/log/src/LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "VENDORPATH/symfony/deprecation-contracts/function.php", "name": "function.php"}]}, "badgeValue": 154, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Health", "method": "index", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "health", "handler": "\\App\\Controllers\\Health::index"}, {"method": "GET", "route": "ping", "handler": "\\App\\Controllers\\Health::ping"}, {"method": "GET", "route": "api/health", "handler": "\\App\\Controllers\\Health::index"}, {"method": "GET", "route": "api/ping", "handler": "\\App\\Controllers\\Health::ping"}, {"method": "GET", "route": "api/auth/me", "handler": "\\App\\Controllers\\Auth::me"}, {"method": "GET", "route": "api/admin/users", "handler": "\\App\\Controllers\\Admin::getUsers"}, {"method": "GET", "route": "api/admin/shops", "handler": "\\App\\Controllers\\Admin::getShops"}, {"method": "GET", "route": "api/admin/analytics", "handler": "\\App\\Controllers\\Admin::getAnalytics"}, {"method": "GET", "route": "api/merchant/dashboard", "handler": "\\App\\Controllers\\Merchant::dashboard"}, {"method": "GET", "route": "api/merchant/shops", "handler": "\\App\\Controllers\\Shops::index"}, {"method": "GET", "route": "api/merchant/shops/new", "handler": "\\App\\Controllers\\Shops::new"}, {"method": "GET", "route": "api/merchant/shops/(.*)/edit", "handler": "\\App\\Controllers\\Shops::edit/$1"}, {"method": "GET", "route": "api/merchant/shops/(.*)", "handler": "\\App\\Controllers\\Shops::show/$1"}, {"method": "GET", "route": "api/shop/([^/]+)/products", "handler": "\\App\\Controllers\\Products::index"}, {"method": "GET", "route": "api/shop/([^/]+)/products/new", "handler": "\\App\\Controllers\\Products::new"}, {"method": "GET", "route": "api/shop/([^/]+)/products/(.*)/edit", "handler": "\\App\\Controllers\\Products::edit/$1"}, {"method": "GET", "route": "api/shop/([^/]+)/products/(.*)", "handler": "\\App\\Controllers\\Products::show/$1"}, {"method": "GET", "route": "api/shop/([^/]+)/categories", "handler": "\\App\\Controllers\\Categories::index"}, {"method": "GET", "route": "api/shop/([^/]+)/categories/new", "handler": "\\App\\Controllers\\Categories::new"}, {"method": "GET", "route": "api/shop/([^/]+)/categories/(.*)/edit", "handler": "\\App\\Controllers\\Categories::edit/$1"}, {"method": "GET", "route": "api/shop/([^/]+)/categories/(.*)", "handler": "\\App\\Controllers\\Categories::show/$1"}, {"method": "GET", "route": "api/shop/([^/]+)/orders", "handler": "\\App\\Controllers\\Orders::index"}, {"method": "GET", "route": "api/shop/([^/]+)/orders/new", "handler": "\\App\\Controllers\\Orders::new"}, {"method": "GET", "route": "api/shop/([^/]+)/orders/(.*)/edit", "handler": "\\App\\Controllers\\Orders::edit/$1"}, {"method": "GET", "route": "api/shop/([^/]+)/orders/(.*)", "handler": "\\App\\Controllers\\Orders::show/$1"}, {"method": "GET", "route": "api/shop/([^/]+)/customers", "handler": "\\App\\Controllers\\Customers::index"}, {"method": "GET", "route": "api/shop/([^/]+)/customers/new", "handler": "\\App\\Controllers\\Customers::new"}, {"method": "GET", "route": "api/shop/([^/]+)/customers/(.*)/edit", "handler": "\\App\\Controllers\\Customers::edit/$1"}, {"method": "GET", "route": "api/shop/([^/]+)/customers/(.*)", "handler": "\\App\\Controllers\\Customers::show/$1"}, {"method": "GET", "route": "admin", "handler": "\\App\\Controllers\\Frontend::adminDashboard"}, {"method": "GET", "route": "admin/login", "handler": "\\App\\Controllers\\Frontend::adminLogin"}, {"method": "GET", "route": "admin/register", "handler": "\\App\\Controllers\\Frontend::adminRegister"}, {"method": "GET", "route": "admin/shops", "handler": "\\App\\Controllers\\Frontend::shops"}, {"method": "GET", "route": "admin/shops/([^/]+)", "handler": "\\App\\Controllers\\Frontend::shopDetails/$1"}, {"method": "GET", "route": "storefront/([^/]+)", "handler": "\\App\\Controllers\\Storefront::index/$1"}, {"method": "GET", "route": "storefront/([^/]+)/products", "handler": "\\App\\Controllers\\Storefront::products/$1"}, {"method": "GET", "route": "storefront/([^/]+)/products/([^/]+)", "handler": "\\App\\Controllers\\Storefront::productDetails/$1/$2"}, {"method": "GET", "route": "storefront/([^/]+)/categories/([^/]+)", "handler": "\\App\\Controllers\\Storefront::category/$1/$2"}, {"method": "GET", "route": "storefront/([^/]+)/checkout", "handler": "\\App\\Controllers\\Storefront::checkout/$1"}, {"method": "POST", "route": "api/auth/register", "handler": "\\App\\Controllers\\Auth::register"}, {"method": "POST", "route": "api/auth/login", "handler": "\\App\\Controllers\\Auth::login"}, {"method": "POST", "route": "api/auth/logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "POST", "route": "api/auth/refresh", "handler": "\\App\\Controllers\\Auth::refresh"}, {"method": "POST", "route": "api/merchant/shops", "handler": "\\App\\Controllers\\Shops::create"}, {"method": "POST", "route": "api/shop/([^/]+)/products", "handler": "\\App\\Controllers\\Products::create"}, {"method": "POST", "route": "api/shop/([^/]+)/categories", "handler": "\\App\\Controllers\\Categories::create"}, {"method": "POST", "route": "api/shop/([^/]+)/orders", "handler": "\\App\\Controllers\\Orders::create"}, {"method": "POST", "route": "api/shop/([^/]+)/customers", "handler": "\\App\\Controllers\\Customers::create"}, {"method": "POST", "route": "api/ai/design/suggestions", "handler": "\\App\\Controllers\\AI::designSuggestions"}, {"method": "POST", "route": "api/ai/products/tagging", "handler": "\\App\\Controllers\\AI::productTagging"}, {"method": "POST", "route": "api/ai/products/pricing", "handler": "\\App\\Controllers\\AI::pricingAnalysis"}, {"method": "POST", "route": "api/ai/marketing/seo", "handler": "\\App\\Controllers\\AI::seoKeywords"}, {"method": "POST", "route": "api/ai/analytics/predictions", "handler": "\\App\\Controllers\\AI::predictions"}, {"method": "POST", "route": "api/ai/chatbot/response", "handler": "\\App\\Controllers\\AI::chatbotResponse"}, {"method": "POST", "route": "storefront/([^/]+)/cart/add", "handler": "\\App\\Controllers\\Storefront::addToCart/$1"}, {"method": "POST", "route": "storefront/([^/]+)/checkout/process", "handler": "\\App\\Controllers\\Storefront::processCheckout/$1"}, {"method": "PATCH", "route": "api/merchant/shops/(.*)", "handler": "\\App\\Controllers\\Shops::update/$1"}, {"method": "PATCH", "route": "api/shop/([^/]+)/products/(.*)", "handler": "\\App\\Controllers\\Products::update/$1"}, {"method": "PATCH", "route": "api/shop/([^/]+)/categories/(.*)", "handler": "\\App\\Controllers\\Categories::update/$1"}, {"method": "PATCH", "route": "api/shop/([^/]+)/orders/(.*)", "handler": "\\App\\Controllers\\Orders::update/$1"}, {"method": "PATCH", "route": "api/shop/([^/]+)/customers/(.*)", "handler": "\\App\\Controllers\\Customers::update/$1"}, {"method": "PUT", "route": "api/merchant/shops/(.*)", "handler": "\\App\\Controllers\\Shops::update/$1"}, {"method": "PUT", "route": "api/shop/([^/]+)/products/(.*)", "handler": "\\App\\Controllers\\Products::update/$1"}, {"method": "PUT", "route": "api/shop/([^/]+)/categories/(.*)", "handler": "\\App\\Controllers\\Categories::update/$1"}, {"method": "PUT", "route": "api/shop/([^/]+)/orders/(.*)", "handler": "\\App\\Controllers\\Orders::update/$1"}, {"method": "PUT", "route": "api/shop/([^/]+)/customers/(.*)", "handler": "\\App\\Controllers\\Customers::update/$1"}, {"method": "DELETE", "route": "api/merchant/shops/(.*)", "handler": "\\App\\Controllers\\Shops::delete/$1"}, {"method": "DELETE", "route": "api/shop/([^/]+)/products/(.*)", "handler": "\\App\\Controllers\\Products::delete/$1"}, {"method": "DELETE", "route": "api/shop/([^/]+)/categories/(.*)", "handler": "\\App\\Controllers\\Categories::delete/$1"}, {"method": "DELETE", "route": "api/shop/([^/]+)/orders/(.*)", "handler": "\\App\\Controllers\\Orders::delete/$1"}, {"method": "DELETE", "route": "api/shop/([^/]+)/customers/(.*)", "handler": "\\App\\Controllers\\Customers::delete/$1"}]}, "badgeValue": 41, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "76.90", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "0.04", "count": 1}}}, "badgeValue": 2, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.163251, "duration": 0.07689809799194336}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.346184, "duration": 3.910064697265625e-05}]}], "vars": {"varData": {"View Data": []}, "session": {"__ci_last_regenerate": "<pre>**********</pre>"}, "headers": {"Host": "shopci.test", "X-Real-Ip": "**********", "X-Forwarded-For": "**********", "X-Forwarded-Proto": "http", "Connection": "close", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-US,en;q=0.9,tr;q=0.8,de;q=0.7"}, "request": "HTTP/1.0", "response": {"statusCode": 200, "reason": "OK", "contentType": "application/json; charset=UTF-8", "headers": {"Content-Type": "application/json; charset=UTF-8"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.28", "phpSAPI": "cli-server", "environment": "development", "baseURL": "http://shopci.test/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}