<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Tests\\Support\\' => array($baseDir . '/tests/_support'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/src'),
    'Psr\\Container\\' => array($vendorDir . '/psr/container/src'),
    'PhpParser\\' => array($vendorDir . '/nikic/php-parser/lib/PhpParser'),
    'Laminas\\Escaper\\' => array($vendorDir . '/laminas/laminas-escaper/src'),
    'Firebase\\JWT\\' => array($vendorDir . '/firebase/php-jwt/src'),
    'Faker\\' => array($vendorDir . '/fakerphp/faker/src/Faker'),
    'DeepCopy\\' => array($vendorDir . '/myclabs/deep-copy/src/DeepCopy'),
    'Config\\' => array($baseDir . '/app/Config'),
    'CodeIgniter\\' => array($vendorDir . '/codeigniter4/framework/system'),
    'App\\' => array($baseDir . '/app'),
);
