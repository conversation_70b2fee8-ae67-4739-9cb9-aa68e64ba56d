<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ShopCI - SaaS E-Commerce Platform</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
        }
        .feature-card {
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-store me-2"></i>ShopCI
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#features">Features</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#pricing">Pricing</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/login">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-primary ms-2" href="/admin/register">Get Started</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section text-center">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <h1 class="display-4 fw-bold mb-4">Build Your E-Commerce Empire</h1>
                    <p class="lead mb-5">Create, manage, and scale your online stores with AI-powered features. ShopCI makes e-commerce simple and powerful.</p>
                    <div class="d-flex gap-3 justify-content-center">
                        <a href="/admin/register" class="btn btn-light btn-lg">
                            <i class="fas fa-rocket me-2"></i>Start Free Trial
                        </a>
                        <a href="#features" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-play me-2"></i>Watch Demo
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center mb-5">
                    <h2 class="display-5 fw-bold">Powerful Features</h2>
                    <p class="lead text-muted">Everything you need to build and grow your online business</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card feature-card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-paint-brush fa-lg"></i>
                            </div>
                            <h5 class="card-title">Drag & Drop Builder</h5>
                            <p class="card-text text-muted">Create stunning storefronts with our intuitive drag-and-drop editor. No coding required.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card feature-card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-robot fa-lg"></i>
                            </div>
                            <h5 class="card-title">AI-Powered Features</h5>
                            <p class="card-text text-muted">Smart product tagging, dynamic pricing, and personalized recommendations powered by AI.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card feature-card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="bg-warning text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-chart-line fa-lg"></i>
                            </div>
                            <h5 class="card-title">Advanced Analytics</h5>
                            <p class="card-text text-muted">Track sales, customer behavior, and get actionable insights to grow your business.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Status Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h3 class="mb-4">System Status</h3>
                    <div class="row g-3">
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">Backend API</h6>
                                    <span class="badge bg-success" id="backend-status">
                                        <i class="fas fa-spinner fa-spin"></i> Checking...
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">AI Server</h6>
                                    <span class="badge bg-success" id="ai-status">
                                        <i class="fas fa-spinner fa-spin"></i> Checking...
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">Database</h6>
                                    <span class="badge bg-success" id="db-status">
                                        <i class="fas fa-spinner fa-spin"></i> Checking...
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">Cache</h6>
                                    <span class="badge bg-success" id="cache-status">
                                        <i class="fas fa-spinner fa-spin"></i> Checking...
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6><i class="fas fa-store me-2"></i>ShopCI</h6>
                    <p class="text-muted mb-0">SaaS E-Commerce Platform</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted mb-0">© 2024 ShopCI. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script>
        // Check system status
        $(document).ready(function() {
            // Check Backend API
            $.get('/api/health')
                .done(function() {
                    $('#backend-status').removeClass('bg-success').addClass('bg-success').html('<i class="fas fa-check"></i> Online');
                })
                .fail(function() {
                    $('#backend-status').removeClass('bg-success').addClass('bg-danger').html('<i class="fas fa-times"></i> Offline');
                });

            // Check AI Server
            $.get('/ai/health')
                .done(function() {
                    $('#ai-status').removeClass('bg-success').addClass('bg-success').html('<i class="fas fa-check"></i> Online');
                })
                .fail(function() {
                    $('#ai-status').removeClass('bg-success').addClass('bg-warning').html('<i class="fas fa-exclamation"></i> Offline');
                });

            // Simulate other checks
            setTimeout(function() {
                $('#db-status').removeClass('bg-success').addClass('bg-success').html('<i class="fas fa-check"></i> Connected');
                $('#cache-status').removeClass('bg-success').addClass('bg-success').html('<i class="fas fa-check"></i> Active');
            }, 1000);
        });
    </script>
</body>
</html>
