<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - ShopCI Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/frontend/assets/css/admin.css" rel="stylesheet">
    
    <style>
        .reset-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .reset-card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            padding: 3rem;
            width: 100%;
            max-width: 400px;
        }
        
        .reset-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .reset-header h1 {
            color: #1f2937;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .reset-header p {
            color: #6b7280;
            margin: 0;
        }
        
        .form-floating {
            margin-bottom: 1rem;
        }
        
        .form-floating input {
            border: 2px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 1rem 0.75rem;
        }
        
        .form-floating input:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .btn-reset {
            background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
            border: none;
            border-radius: 0.5rem;
            padding: 0.75rem;
            font-weight: 600;
            width: 100%;
            margin-bottom: 1rem;
        }
        
        .btn-reset:hover {
            transform: translateY(-1px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        
        .password-strength {
            margin-top: 0.5rem;
            font-size: 0.875rem;
        }
        
        .strength-weak { color: #dc2626; }
        .strength-medium { color: #f59e0b; }
        .strength-strong { color: #10b981; }
        
        .divider {
            text-align: center;
            margin: 1.5rem 0;
            position: relative;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e5e7eb;
        }
        
        .divider span {
            background: white;
            padding: 0 1rem;
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .back-link {
            text-align: center;
            margin-top: 1rem;
        }
        
        .back-link a {
            color: #2563eb;
            text-decoration: none;
            font-weight: 500;
        }
        
        .back-link a:hover {
            text-decoration: underline;
        }
        
        .token-invalid {
            text-align: center;
            padding: 2rem;
        }
        
        .token-invalid .icon {
            font-size: 4rem;
            color: #dc2626;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-card">
            <!-- Valid Token Form -->
            <div id="resetForm" style="display: none;">
                <div class="reset-header">
                    <h1><i class="fas fa-lock text-primary"></i></h1>
                    <h2>Reset Password</h2>
                    <p>Enter your new password below.</p>
                </div>
                
                <!-- Alerts Container -->
                <div class="alerts-container"></div>
                
                <form id="passwordResetForm" class="needs-validation" novalidate>
                    <input type="hidden" id="reset_token" name="token" value="">
                    
                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" name="password" placeholder="New Password" required minlength="6">
                        <label for="password"><i class="fas fa-lock me-2"></i>New Password</label>
                        <div class="invalid-feedback">
                            Password must be at least 6 characters long.
                        </div>
                        <div class="password-strength" id="passwordStrength"></div>
                    </div>
                    
                    <div class="form-floating">
                        <input type="password" class="form-control" id="password_confirm" name="password_confirm" placeholder="Confirm Password" required>
                        <label for="password_confirm"><i class="fas fa-lock me-2"></i>Confirm Password</label>
                        <div class="invalid-feedback">
                            Please confirm your password.
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-reset" data-original-text="Reset Password">
                        <i class="fas fa-key me-2"></i>Reset Password
                    </button>
                </form>
                
                <div class="divider">
                    <span>Remember your password?</span>
                </div>
                
                <div class="back-link">
                    <a href="/admin/login">Back to Sign In</a>
                </div>
            </div>
            
            <!-- Invalid Token Message -->
            <div id="invalidToken" style="display: none;">
                <div class="token-invalid">
                    <div class="icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3>Invalid or Expired Link</h3>
                    <p class="text-muted mb-4">This password reset link is invalid or has expired. Please request a new one.</p>
                    <a href="/admin/forgot-password" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-2"></i>Request New Link
                    </a>
                    <div class="mt-3">
                        <a href="/admin/login" class="text-muted">Back to Sign In</a>
                    </div>
                </div>
            </div>
            
            <!-- Loading State -->
            <div id="loadingState">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3 text-muted">Validating reset link...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Custom JS -->
    <script src="/frontend/assets/js/admin.js"></script>
    
    <script>
        $(document).ready(function() {
            const urlParams = new URLSearchParams(window.location.search);
            const token = urlParams.get('token');
            
            if (!token) {
                showInvalidToken();
                return;
            }
            
            $('#reset_token').val(token);
            validateToken(token);
            
            // Password strength checker
            $('#password').on('input', function() {
                checkPasswordStrength($(this).val());
            });
            
            // Password confirmation validation
            $('#password_confirm').on('input', function() {
                const password = $('#password').val();
                const confirm = $(this).val();
                
                if (confirm && password !== confirm) {
                    $(this)[0].setCustomValidity('Passwords do not match');
                } else {
                    $(this)[0].setCustomValidity('');
                }
            });
            
            // Form submission
            $('#passwordResetForm').on('submit', function(e) {
                e.preventDefault();
                handlePasswordReset(this);
            });
        });
        
        async function validateToken(token) {
            // For now, we'll assume the token is valid and show the form
            // In a real implementation, you might want to validate the token first
            showResetForm();
        }
        
        function showResetForm() {
            $('#loadingState').hide();
            $('#resetForm').show();
        }
        
        function showInvalidToken() {
            $('#loadingState').hide();
            $('#invalidToken').show();
        }
        
        async function handlePasswordReset(form) {
            const formData = new FormData(form);
            const password = formData.get('password');
            const passwordConfirm = formData.get('password_confirm');
            
            if (password !== passwordConfirm) {
                showAlert('Passwords do not match', 'danger');
                return;
            }
            
            const data = {
                token: formData.get('token'),
                password: password
            };

            showLoading(form);

            try {
                const response = await $.ajax({
                    url: '/api/auth/reset-password',
                    method: 'POST',
                    data: JSON.stringify(data),
                    contentType: 'application/json'
                });

                if (response.success) {
                    showAlert('Password reset successful! You can now sign in with your new password.', 'success');
                    
                    setTimeout(() => {
                        window.location.href = '/admin/login';
                    }, 2000);
                } else {
                    showAlert(response.message || 'Failed to reset password', 'danger');
                }
            } catch (error) {
                console.error('Password reset error:', error);
                if (error.status === 400) {
                    showAlert('Invalid or expired reset token. Please request a new password reset.', 'danger');
                    setTimeout(() => {
                        showInvalidToken();
                    }, 2000);
                } else {
                    showAlert('Failed to reset password. Please try again.', 'danger');
                }
            } finally {
                hideLoading(form);
            }
        }
        
        function checkPasswordStrength(password) {
            const strengthDiv = $('#passwordStrength');
            
            if (!password) {
                strengthDiv.html('');
                return;
            }
            
            let strength = 0;
            let feedback = [];
            
            // Length check
            if (password.length >= 8) strength++;
            else feedback.push('At least 8 characters');
            
            // Uppercase check
            if (/[A-Z]/.test(password)) strength++;
            else feedback.push('One uppercase letter');
            
            // Lowercase check
            if (/[a-z]/.test(password)) strength++;
            else feedback.push('One lowercase letter');
            
            // Number check
            if (/\d/.test(password)) strength++;
            else feedback.push('One number');
            
            // Special character check
            if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++;
            else feedback.push('One special character');
            
            let strengthText = '';
            let strengthClass = '';
            
            if (strength < 2) {
                strengthText = 'Weak';
                strengthClass = 'strength-weak';
            } else if (strength < 4) {
                strengthText = 'Medium';
                strengthClass = 'strength-medium';
            } else {
                strengthText = 'Strong';
                strengthClass = 'strength-strong';
            }
            
            strengthDiv.html(`
                <div class="${strengthClass}">
                    <i class="fas fa-shield-alt me-1"></i>
                    Password strength: ${strengthText}
                    ${feedback.length > 0 ? '<br><small>Missing: ' + feedback.join(', ') + '</small>' : ''}
                </div>
            `);
        }

        function showLoading(element) {
            const $element = $(element);
            const $submitBtn = $element.find('[type="submit"]');
            
            $submitBtn.prop('disabled', true);
            $submitBtn.html('<span class="spinner-border spinner-border-sm me-2"></span>Resetting...');
        }

        function hideLoading(element) {
            const $element = $(element);
            const $submitBtn = $element.find('[type="submit"]');
            
            $submitBtn.prop('disabled', false);
            $submitBtn.html($submitBtn.data('original-text') || 'Reset Password');
        }

        function showAlert(message, type = 'info') {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            $('.alerts-container').prepend(alertHtml);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                $('.alert').first().fadeOut(() => {
                    $(this).remove();
                });
            }, 5000);
        }

        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
</body>
</html>
