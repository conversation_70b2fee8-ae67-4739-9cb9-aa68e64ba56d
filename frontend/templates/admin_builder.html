<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Storefront Builder - ShopCI</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Sortable.js for drag and drop -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: #f8f9fa;
            overflow-x: hidden;
        }
        
        .builder-container {
            display: flex;
            height: 100vh;
        }
        
        .builder-sidebar {
            width: 300px;
            background: white;
            border-right: 1px solid #e9ecef;
            overflow-y: auto;
            flex-shrink: 0;
        }
        
        .builder-main {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .builder-toolbar {
            background: white;
            border-bottom: 1px solid #e9ecef;
            padding: 1rem;
            display: flex;
            justify-content: between;
            align-items: center;
        }
        
        .builder-canvas {
            flex: 1;
            background: #f8f9fa;
            overflow-y: auto;
            position: relative;
        }
        
        .canvas-container {
            max-width: 1200px;
            margin: 2rem auto;
            background: white;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            min-height: 800px;
            position: relative;
        }
        
        .component-library {
            padding: 1rem;
        }
        
        .component-category {
            margin-bottom: 1.5rem;
        }
        
        .component-category h6 {
            color: #6b7280;
            font-weight: 600;
            margin-bottom: 0.75rem;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.05em;
        }
        
        .component-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.5rem;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            cursor: grab;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
        }
        
        .component-item:hover {
            background: #e9ecef;
            border-color: #2563eb;
            transform: translateY(-1px);
        }
        
        .component-item:active {
            cursor: grabbing;
        }
        
        .component-item i {
            color: #2563eb;
            margin-right: 0.75rem;
            width: 20px;
            text-align: center;
        }
        
        .component-item .component-info {
            flex: 1;
        }
        
        .component-item .component-name {
            font-weight: 500;
            margin-bottom: 0.25rem;
        }
        
        .component-item .component-desc {
            font-size: 0.75rem;
            color: #6b7280;
        }
        
        .drop-zone {
            min-height: 100px;
            border: 2px dashed #d1d5db;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
            margin: 1rem;
            transition: all 0.2s ease;
        }
        
        .drop-zone.drag-over {
            border-color: #2563eb;
            background: #eff6ff;
            color: #2563eb;
        }
        
        .component-wrapper {
            position: relative;
            margin: 0.5rem;
            border: 2px solid transparent;
            border-radius: 0.25rem;
            transition: all 0.2s ease;
        }
        
        .component-wrapper:hover {
            border-color: #2563eb;
        }
        
        .component-wrapper.selected {
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .component-controls {
            position: absolute;
            top: -35px;
            right: 0;
            background: #2563eb;
            border-radius: 0.25rem;
            padding: 0.25rem;
            opacity: 0;
            transition: opacity 0.2s ease;
        }
        
        .component-wrapper:hover .component-controls,
        .component-wrapper.selected .component-controls {
            opacity: 1;
        }
        
        .component-controls button {
            background: none;
            border: none;
            color: white;
            padding: 0.25rem;
            margin: 0 0.125rem;
            border-radius: 0.125rem;
            cursor: pointer;
            font-size: 0.75rem;
        }
        
        .component-controls button:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .properties-panel {
            width: 300px;
            background: white;
            border-left: 1px solid #e9ecef;
            padding: 1rem;
            overflow-y: auto;
        }
        
        .properties-panel h6 {
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .property-group {
            margin-bottom: 1.5rem;
        }
        
        .property-group label {
            font-weight: 500;
            margin-bottom: 0.5rem;
            display: block;
            font-size: 0.875rem;
        }
        
        .btn-preview {
            background: #10b981;
            border-color: #10b981;
        }
        
        .btn-preview:hover {
            background: #059669;
            border-color: #059669;
        }
        
        .btn-publish {
            background: #f59e0b;
            border-color: #f59e0b;
        }
        
        .btn-publish:hover {
            background: #d97706;
            border-color: #d97706;
        }
        
        .device-preview {
            display: flex;
            gap: 0.5rem;
            margin-left: auto;
        }
        
        .device-btn {
            background: none;
            border: 1px solid #d1d5db;
            padding: 0.5rem;
            border-radius: 0.25rem;
            cursor: pointer;
            color: #6b7280;
        }
        
        .device-btn.active {
            background: #2563eb;
            border-color: #2563eb;
            color: white;
        }
        
        .canvas-container.mobile {
            max-width: 375px;
        }
        
        .canvas-container.tablet {
            max-width: 768px;
        }
        
        .sortable-ghost {
            opacity: 0.5;
        }
        
        .sortable-chosen {
            transform: rotate(5deg);
        }
    </style>
</head>
<body>
    <div class="builder-container">
        <!-- Sidebar with Components -->
        <div class="builder-sidebar">
            <div class="component-library">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0">Components</h5>
                    <button class="btn btn-sm btn-outline-secondary" onclick="goBack()">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                </div>
                
                <div id="componentLibrary">
                    <div class="text-center py-3">
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2 mb-0 text-muted small">Loading components...</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Builder Area -->
        <div class="builder-main">
            <!-- Toolbar -->
            <div class="builder-toolbar">
                <div class="d-flex align-items-center">
                    <h6 class="mb-0 me-3" id="shopName">Loading...</h6>
                    <span class="badge bg-secondary" id="pageName">Home</span>
                </div>
                
                <div class="device-preview">
                    <button class="device-btn active" data-device="desktop" title="Desktop">
                        <i class="fas fa-desktop"></i>
                    </button>
                    <button class="device-btn" data-device="tablet" title="Tablet">
                        <i class="fas fa-tablet-alt"></i>
                    </button>
                    <button class="device-btn" data-device="mobile" title="Mobile">
                        <i class="fas fa-mobile-alt"></i>
                    </button>
                </div>
                
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-secondary btn-sm" onclick="saveChanges()">
                        <i class="fas fa-save me-1"></i>Save
                    </button>
                    <button class="btn btn-preview btn-sm text-white" onclick="previewSite()">
                        <i class="fas fa-eye me-1"></i>Preview
                    </button>
                    <button class="btn btn-publish btn-sm text-white" onclick="publishSite()">
                        <i class="fas fa-rocket me-1"></i>Publish
                    </button>
                </div>
            </div>
            
            <!-- Canvas -->
            <div class="builder-canvas">
                <div class="canvas-container" id="canvas">
                    <div class="drop-zone" id="mainDropZone">
                        <div class="text-center">
                            <i class="fas fa-plus-circle fa-2x mb-2"></i>
                            <p class="mb-0">Drag components here to start building</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Properties Panel -->
        <div class="properties-panel" id="propertiesPanel" style="display: none;">
            <h6>Component Properties</h6>
            <div id="propertiesContent">
                <p class="text-muted">Select a component to edit its properties</p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <script>
        let currentShopId = null;
        let currentPage = 'home';
        let selectedComponent = null;
        let components = {};
        let pageData = { components: [] };

        $(document).ready(function() {
            // Get shop ID from URL
            const pathParts = window.location.pathname.split('/');
            currentShopId = pathParts[pathParts.length - 1];
            
            if (!currentShopId || isNaN(currentShopId)) {
                alert('Invalid shop ID');
                goBack();
                return;
            }
            
            initializeBuilder();
        });

        async function initializeBuilder() {
            try {
                // Load components library
                await loadComponents();
                
                // Load shop data
                await loadShopData();
                
                // Load page data
                await loadPageData();
                
                // Initialize drag and drop
                initializeDragAndDrop();
                
                // Initialize device preview
                initializeDevicePreview();
                
            } catch (error) {
                console.error('Failed to initialize builder:', error);
                alert('Failed to load builder. Please try again.');
            }
        }

        async function loadComponents() {
            try {
                const response = await $.get('/api/builder/components');
                if (response.success) {
                    components = response.data;
                    renderComponentLibrary();
                }
            } catch (error) {
                console.error('Failed to load components:', error);
            }
        }

        function renderComponentLibrary() {
            const library = $('#componentLibrary');
            let html = '';
            
            Object.keys(components).forEach(category => {
                html += `
                    <div class="component-category">
                        <h6>${category.charAt(0).toUpperCase() + category.slice(1)}</h6>
                `;
                
                components[category].forEach(component => {
                    html += `
                        <div class="component-item" data-component-type="${component.id}">
                            <i class="${component.icon}"></i>
                            <div class="component-info">
                                <div class="component-name">${component.name}</div>
                                <div class="component-desc">${component.description}</div>
                            </div>
                        </div>
                    `;
                });
                
                html += '</div>';
            });
            
            library.html(html);
        }

        async function loadShopData() {
            try {
                const response = await $.get(`/api/shops/${currentShopId}`);
                if (response.success) {
                    $('#shopName').text(response.data.name);
                }
            } catch (error) {
                console.error('Failed to load shop data:', error);
            }
        }

        async function loadPageData() {
            try {
                const response = await $.get(`/api/builder/${currentShopId}/page/${currentPage}`);
                if (response.success) {
                    pageData = response.data;
                    renderPage();
                }
            } catch (error) {
                console.error('Failed to load page data:', error);
            }
        }

        function renderPage() {
            const canvas = $('#canvas');
            
            if (pageData.components.length === 0) {
                canvas.html(`
                    <div class="drop-zone" id="mainDropZone">
                        <div class="text-center">
                            <i class="fas fa-plus-circle fa-2x mb-2"></i>
                            <p class="mb-0">Drag components here to start building</p>
                        </div>
                    </div>
                `);
            } else {
                let html = '';
                pageData.components.forEach((component, index) => {
                    html += renderComponent(component, index);
                });
                canvas.html(html);
            }
            
            // Reinitialize sortable
            initializeDragAndDrop();
        }

        function renderComponent(component, index) {
            // This is a simplified render - in production you'd have actual component templates
            return `
                <div class="component-wrapper" data-component-id="${component.id}" data-index="${index}">
                    <div class="component-controls">
                        <button onclick="editComponent(${index})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="duplicateComponent(${index})" title="Duplicate">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button onclick="deleteComponent(${index})" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="component-content">
                        ${getComponentHTML(component)}
                    </div>
                </div>
            `;
        }

        function getComponentHTML(component) {
            // Simplified component rendering
            switch (component.type) {
                case 'header':
                    return `
                        <nav class="navbar navbar-expand-lg" style="background-color: ${component.settings.background_color || '#ffffff'}; color: ${component.settings.text_color || '#333333'};">
                            <div class="container">
                                <a class="navbar-brand" href="#">
                                    ${component.settings.logo ? `<img src="${component.settings.logo}" alt="Logo" height="40">` : 'Logo'}
                                </a>
                                <div class="navbar-nav ms-auto">
                                    ${(component.settings.menu_items || []).map(item => `<a class="nav-link" href="${item.url}">${item.label}</a>`).join('')}
                                </div>
                            </div>
                        </nav>
                    `;
                case 'hero':
                    return `
                        <div class="hero-section py-5" style="background-image: url('${component.settings.background_image || ''}'); background-size: cover; background-position: center;">
                            <div class="container text-center">
                                <h1 class="display-4 mb-3">${component.settings.title || 'Hero Title'}</h1>
                                <p class="lead mb-4">${component.settings.subtitle || 'Hero subtitle'}</p>
                                <a href="${component.settings.button_link || '#'}" class="btn btn-primary btn-lg">
                                    ${component.settings.button_text || 'Call to Action'}
                                </a>
                            </div>
                        </div>
                    `;
                case 'footer':
                    return `
                        <footer class="py-4" style="background-color: ${component.settings.background_color || '#f8f9fa'};">
                            <div class="container text-center">
                                <p class="mb-0">${component.settings.copyright_text || '© 2024 Your Store. All rights reserved.'}</p>
                            </div>
                        </footer>
                    `;
                default:
                    return `
                        <div class="p-4 text-center bg-light">
                            <h5>${component.type}</h5>
                            <p class="text-muted">Component preview</p>
                        </div>
                    `;
            }
        }

        function initializeDragAndDrop() {
            // Make component library items draggable
            $('.component-item').each(function() {
                $(this).attr('draggable', true);
                $(this).on('dragstart', function(e) {
                    e.originalEvent.dataTransfer.setData('text/plain', $(this).data('component-type'));
                });
            });
            
            // Make canvas droppable
            const canvas = document.getElementById('canvas');
            if (canvas) {
                new Sortable(canvas, {
                    group: 'components',
                    animation: 150,
                    ghostClass: 'sortable-ghost',
                    chosenClass: 'sortable-chosen',
                    onAdd: function(evt) {
                        const componentType = evt.item.dataset.componentType;
                        if (componentType) {
                            addComponent(componentType, evt.newIndex);
                            evt.item.remove(); // Remove the dragged element
                        }
                    },
                    onUpdate: function(evt) {
                        // Handle reordering
                        const oldIndex = evt.oldIndex;
                        const newIndex = evt.newIndex;
                        
                        // Move component in data
                        const component = pageData.components.splice(oldIndex, 1)[0];
                        pageData.components.splice(newIndex, 0, component);
                        
                        renderPage();
                    }
                });
            }
        }

        function addComponent(componentType, index = -1) {
            // Find component definition
            let componentDef = null;
            Object.values(components).forEach(category => {
                const found = category.find(c => c.id === componentType);
                if (found) componentDef = found;
            });
            
            if (!componentDef) return;
            
            // Create new component instance
            const newComponent = {
                id: 'comp-' + Date.now(),
                type: componentType,
                settings: {}
            };
            
            // Set default settings
            if (componentDef.settings) {
                Object.keys(componentDef.settings).forEach(key => {
                    const setting = componentDef.settings[key];
                    newComponent.settings[key] = setting.default || '';
                });
            }
            
            // Add to page data
            if (index === -1) {
                pageData.components.push(newComponent);
            } else {
                pageData.components.splice(index, 0, newComponent);
            }
            
            renderPage();
        }

        function editComponent(index) {
            selectedComponent = index;
            const component = pageData.components[index];
            
            // Highlight selected component
            $('.component-wrapper').removeClass('selected');
            $(`.component-wrapper[data-index="${index}"]`).addClass('selected');
            
            // Show properties panel
            showPropertiesPanel(component);
        }

        function showPropertiesPanel(component) {
            const panel = $('#propertiesPanel');
            const content = $('#propertiesContent');
            
            // Find component definition
            let componentDef = null;
            Object.values(components).forEach(category => {
                const found = category.find(c => c.id === component.type);
                if (found) componentDef = found;
            });
            
            if (!componentDef || !componentDef.settings) {
                content.html('<p class="text-muted">No properties available</p>');
                panel.show();
                return;
            }
            
            let html = '';
            Object.keys(componentDef.settings).forEach(key => {
                const setting = componentDef.settings[key];
                const value = component.settings[key] || '';
                
                html += `
                    <div class="property-group">
                        <label for="prop-${key}">${setting.label}</label>
                `;
                
                switch (setting.type) {
                    case 'text':
                        html += `<input type="text" class="form-control" id="prop-${key}" value="${value}" onchange="updateProperty('${key}', this.value)">`;
                        break;
                    case 'color':
                        html += `<input type="color" class="form-control" id="prop-${key}" value="${value}" onchange="updateProperty('${key}', this.value)">`;
                        break;
                    case 'select':
                        html += `<select class="form-select" id="prop-${key}" onchange="updateProperty('${key}', this.value)">`;
                        setting.options.forEach(option => {
                            html += `<option value="${option}" ${value === option ? 'selected' : ''}>${option}</option>`;
                        });
                        html += '</select>';
                        break;
                    default:
                        html += `<input type="text" class="form-control" id="prop-${key}" value="${value}" onchange="updateProperty('${key}', this.value)">`;
                }
                
                html += '</div>';
            });
            
            content.html(html);
            panel.show();
        }

        function updateProperty(key, value) {
            if (selectedComponent !== null) {
                pageData.components[selectedComponent].settings[key] = value;
                renderPage();
                // Re-select the component
                editComponent(selectedComponent);
            }
        }

        function duplicateComponent(index) {
            const component = JSON.parse(JSON.stringify(pageData.components[index]));
            component.id = 'comp-' + Date.now();
            pageData.components.splice(index + 1, 0, component);
            renderPage();
        }

        function deleteComponent(index) {
            if (confirm('Are you sure you want to delete this component?')) {
                pageData.components.splice(index, 1);
                renderPage();
                $('#propertiesPanel').hide();
                selectedComponent = null;
            }
        }

        function initializeDevicePreview() {
            $('.device-btn').on('click', function() {
                $('.device-btn').removeClass('active');
                $(this).addClass('active');
                
                const device = $(this).data('device');
                const canvas = $('.canvas-container');
                
                canvas.removeClass('mobile tablet');
                if (device !== 'desktop') {
                    canvas.addClass(device);
                }
            });
        }

        async function saveChanges() {
            try {
                const response = await $.ajax({
                    url: `/api/builder/${currentShopId}/page`,
                    method: 'POST',
                    data: JSON.stringify(pageData),
                    contentType: 'application/json'
                });
                
                if (response.success) {
                    showAlert('Changes saved successfully!', 'success');
                } else {
                    showAlert('Failed to save changes', 'danger');
                }
            } catch (error) {
                console.error('Save error:', error);
                showAlert('Failed to save changes', 'danger');
            }
        }

        async function previewSite() {
            try {
                const response = await $.get(`/api/builder/${currentShopId}/preview`);
                if (response.success) {
                    window.open(response.data.preview_url, '_blank');
                }
            } catch (error) {
                console.error('Preview error:', error);
                showAlert('Failed to generate preview', 'danger');
            }
        }

        async function publishSite() {
            if (confirm('Are you sure you want to publish this site? It will be live for visitors.')) {
                try {
                    const response = await $.ajax({
                        url: `/api/builder/${currentShopId}/publish`,
                        method: 'POST'
                    });
                    
                    if (response.success) {
                        showAlert('Site published successfully!', 'success');
                        if (response.data.live_url) {
                            setTimeout(() => {
                                window.open(response.data.live_url, '_blank');
                            }, 1000);
                        }
                    } else {
                        showAlert('Failed to publish site', 'danger');
                    }
                } catch (error) {
                    console.error('Publish error:', error);
                    showAlert('Failed to publish site', 'danger');
                }
            }
        }

        function goBack() {
            window.location.href = '/admin/shops';
        }

        function showAlert(message, type = 'info') {
            // Create a temporary alert
            const alert = $(`
                <div class="alert alert-${type} alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `);
            
            $('body').append(alert);
            
            setTimeout(() => {
                alert.fadeOut(() => alert.remove());
            }, 3000);
        }
    </script>
</body>
</html>
