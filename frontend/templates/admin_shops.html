<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shops - ShopCI Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/frontend/assets/css/admin.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-store me-2"></i>ShopCI</h3>
        </div>
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="/admin/">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="/admin/shops">
                        <i class="fas fa-store"></i>
                        Shops
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/admin/users">
                        <i class="fas fa-users"></i>
                        Users
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/admin/analytics">
                        <i class="fas fa-chart-line"></i>
                        Analytics
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/admin/settings">
                        <i class="fas fa-cog"></i>
                        Settings
                    </a>
                </li>
            </ul>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="topbar">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <button class="btn btn-link sidebar-toggle d-md-none">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h4 class="mb-0">Shops</h4>
                </div>
                <div class="d-flex align-items-center">
                    <button class="btn btn-primary me-3" data-bs-toggle="modal" data-bs-target="#createShopModal">
                        <i class="fas fa-plus me-2"></i>Create Shop
                    </button>
                    <div class="dropdown">
                        <button class="btn btn-link dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-2"></i>
                            <span class="user-name">Loading...</span>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/admin/profile"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="/admin/settings"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item logout-btn" href="#"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Wrapper -->
        <div class="content-wrapper">
            <!-- Alerts Container -->
            <div class="alerts-container"></div>

            <!-- Shops Grid -->
            <div class="row" id="shopsGrid">
                <div class="col-12 text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3 text-muted">Loading shops...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Shop Modal -->
    <div class="modal fade" id="createShopModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create New Shop</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="createShopForm" class="needs-validation" novalidate>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="shopName" class="form-label">Shop Name *</label>
                                    <input type="text" class="form-control" id="shopName" name="name" required>
                                    <div class="invalid-feedback">Please provide a shop name.</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="shopSubdomain" class="form-label">Subdomain *</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="shopSubdomain" name="subdomain" required>
                                        <span class="input-group-text">.shopci.test</span>
                                    </div>
                                    <div class="invalid-feedback">Please provide a subdomain.</div>
                                    <div class="form-text">This will be your shop's URL</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="shopDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="shopDescription" name="description" rows="3" placeholder="Brief description of your shop"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="shopTheme" class="form-label">Theme</label>
                            <select class="form-select" id="shopTheme" name="theme">
                                <option value="default">Default</option>
                                <option value="modern">Modern</option>
                                <option value="classic">Classic</option>
                                <option value="minimal">Minimal</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="customDomain" class="form-label">Custom Domain (Optional)</label>
                            <input type="text" class="form-control" id="customDomain" name="custom_domain" placeholder="www.yourstore.com">
                            <div class="form-text">You can add a custom domain later</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary" data-original-text="Create Shop">
                            <i class="fas fa-plus me-2"></i>Create Shop
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Custom JS -->
    <script src="/frontend/assets/js/admin.js"></script>
    
    <script>
        $(document).ready(function() {
            loadShops();
            
            // Create shop form submission
            $('#createShopForm').on('submit', function(e) {
                e.preventDefault();
                createShop(this);
            });
            
            // Subdomain validation
            $('#shopSubdomain').on('input', function() {
                let subdomain = $(this).val().toLowerCase().replace(/[^a-z0-9-]/g, '');
                $(this).val(subdomain);
            });
        });

        async function loadShops() {
            try {
                const response = await $.get('/api/shops/my');
                
                if (response.success) {
                    displayShops(response.data);
                } else {
                    showAlert('Failed to load shops', 'danger');
                }
            } catch (error) {
                console.error('Error loading shops:', error);
                showAlert('Failed to load shops', 'danger');
            }
        }

        function displayShops(shops) {
            const grid = $('#shopsGrid');
            
            if (shops.length === 0) {
                grid.html(`
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-store fa-4x text-muted mb-3"></i>
                        <h4>No shops yet</h4>
                        <p class="text-muted">Create your first shop to get started</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createShopModal">
                            <i class="fas fa-plus me-2"></i>Create Your First Shop
                        </button>
                    </div>
                `);
                return;
            }

            let html = '';
            shops.forEach(shop => {
                html += `
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card shop-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <h5 class="card-title mb-0">${shop.name}</h5>
                                    <span class="badge bg-${shop.status === 'active' ? 'success' : 'warning'}">${shop.status}</span>
                                </div>
                                <p class="card-text text-muted">${shop.description || 'No description'}</p>
                                <div class="mb-3">
                                    <small class="text-muted">
                                        <i class="fas fa-link me-1"></i>
                                        ${shop.subdomain}.shopci.test
                                    </small>
                                </div>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-primary btn-sm" onclick="openBuilder(${shop.id})">
                                        <i class="fas fa-edit me-1"></i>Edit
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" onclick="previewShop(${shop.id})">
                                        <i class="fas fa-eye me-1"></i>Preview
                                    </button>
                                    <div class="dropdown">
                                        <button class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" onclick="editShop(${shop.id})">
                                                <i class="fas fa-cog me-2"></i>Settings
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="duplicateShop(${shop.id})">
                                                <i class="fas fa-copy me-2"></i>Duplicate
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger" href="#" onclick="deleteShop(${shop.id})">
                                                <i class="fas fa-trash me-2"></i>Delete
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            grid.html(html);
        }

        async function createShop(form) {
            const formData = new FormData(form);
            const data = {
                name: formData.get('name'),
                subdomain: formData.get('subdomain'),
                description: formData.get('description'),
                theme: formData.get('theme'),
                custom_domain: formData.get('custom_domain')
            };

            showLoading(form);

            try {
                const response = await $.ajax({
                    url: '/api/shops',
                    method: 'POST',
                    data: JSON.stringify(data),
                    contentType: 'application/json'
                });

                if (response.success) {
                    showAlert('Shop created successfully!', 'success');
                    $('#createShopModal').modal('hide');
                    form.reset();
                    loadShops();
                } else {
                    showAlert(response.message || 'Failed to create shop', 'danger');
                    if (response.errors) {
                        Object.values(response.errors).forEach(error => {
                            showAlert(error, 'danger');
                        });
                    }
                }
            } catch (error) {
                console.error('Create shop error:', error);
                showAlert('Failed to create shop. Please try again.', 'danger');
            } finally {
                hideLoading(form);
            }
        }

        function openBuilder(shopId) {
            window.location.href = `/admin/builder/${shopId}`;
        }

        function previewShop(shopId) {
            window.open(`/preview/${shopId}`, '_blank');
        }

        function editShop(shopId) {
            // TODO: Implement shop settings modal
            showAlert('Shop settings coming soon!', 'info');
        }

        function duplicateShop(shopId) {
            // TODO: Implement shop duplication
            showAlert('Shop duplication coming soon!', 'info');
        }

        function deleteShop(shopId) {
            if (confirm('Are you sure you want to delete this shop? This action cannot be undone.')) {
                // TODO: Implement shop deletion
                showAlert('Shop deletion coming soon!', 'info');
            }
        }

        function showLoading(element) {
            const $element = $(element);
            const $submitBtn = $element.find('[type="submit"]');
            
            $submitBtn.prop('disabled', true);
            $submitBtn.html('<span class="spinner-border spinner-border-sm me-2"></span>Creating...');
        }

        function hideLoading(element) {
            const $element = $(element);
            const $submitBtn = $element.find('[type="submit"]');
            
            $submitBtn.prop('disabled', false);
            $submitBtn.html($submitBtn.data('original-text') || 'Create Shop');
        }

        function showAlert(message, type = 'info') {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            $('.alerts-container').prepend(alertHtml);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                $('.alert').first().fadeOut(() => {
                    $(this).remove();
                });
            }, 5000);
        }
    </script>
</body>
</html>
