/**
 * ShopCI Admin Panel JavaScript
 */

class ShopCIAdmin {
    constructor() {
        this.apiBaseUrl = '/api';
        this.token = localStorage.getItem('shopci_token');
        this.user = JSON.parse(localStorage.getItem('shopci_user') || 'null');
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupAjaxDefaults();
        // this.checkAuthentication();
    }

    setupEventListeners() {
        // Sidebar toggle for mobile
        $(document).on('click', '.sidebar-toggle', () => {
            $('.sidebar').toggleClass('show');
        });

        // Login form
        $(document).on('submit', '#loginForm', (e) => {
            e.preventDefault();
            this.handleLogin(e.target);
        });

        // Register form
        $(document).on('submit', '#registerForm', (e) => {
            e.preventDefault();
            this.handleRegister(e.target);
        });

        // Logout
        $(document).on('click', '.logout-btn', () => {
            this.logout();
        });

        // Generic form submissions
        $(document).on('submit', '.ajax-form', (e) => {
            e.preventDefault();
            this.handleAjaxForm(e.target);
        });
    }

    setupAjaxDefaults() {
        $.ajaxSetup({
            beforeSend: (xhr) => {
                if (this.token) {
                    xhr.setRequestHeader('Authorization', `Bearer ${this.token}`);
                }
                xhr.setRequestHeader('Content-Type', 'application/json');
            },
            error: (xhr) => {
                if (xhr.status === 401) {
                    this.logout();
                    this.showAlert('Session expired. Please login again.', 'warning');
                }
            }
        });
    }

    checkAuthentication() {
        if (!this.token && this.requiresAuth()) {
            window.location.href = '/admin/login';
            return;
        }

        if (this.token) {
            this.validateToken();
        }
    }

    requiresAuth() {
        const publicPages = ['/admin/login', '/admin/register'];
        return !publicPages.includes(window.location.pathname);
    }

    async validateToken() {
        try {
            const response = await $.get(`${this.apiBaseUrl}/auth/me`);
            if (response.success) {
                this.user = response.data.user;
                localStorage.setItem('shopci_user', JSON.stringify(this.user));
                this.updateUserInterface();
            }
        } catch (error) {
            console.error('Token validation failed:', error);
            this.logout();
        }
    }

    async handleLogin(form) {
        const formData = new FormData(form);
        const data = {
            email: formData.get('email'),
            password: formData.get('password')
        };

        this.showLoading(form);

        try {
            const response = await $.ajax({
                url: `${this.apiBaseUrl}/auth/login`,
                method: 'POST',
                data: JSON.stringify(data),
                contentType: 'application/json'
            });

            if (response.success) {
                this.token = response.data.token;
                this.user = response.data.user;
                
                localStorage.setItem('shopci_token', this.token);
                localStorage.setItem('shopci_user', JSON.stringify(this.user));
                
                this.showAlert('Login successful!', 'success');
                
                setTimeout(() => {
                    window.location.href = '/admin/';
                }, 1000);
            } else {
                this.showAlert(response.message || 'Login failed', 'danger');
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showAlert('Login failed. Please try again.', 'danger');
        } finally {
            this.hideLoading(form);
        }
    }

    async handleRegister(form) {
        const formData = new FormData(form);
        const data = {
            email: formData.get('email'),
            password: formData.get('password'),
            first_name: formData.get('first_name'),
            last_name: formData.get('last_name'),
            role: formData.get('role') || 'merchant'
        };

        // Validate password confirmation
        if (data.password !== formData.get('password_confirm')) {
            this.showAlert('Passwords do not match', 'danger');
            return;
        }

        this.showLoading(form);

        try {
            const response = await $.ajax({
                url: `${this.apiBaseUrl}/auth/register`,
                method: 'POST',
                data: JSON.stringify(data),
                contentType: 'application/json'
            });

            if (response.success) {
                this.token = response.data.token;
                this.user = response.data.user;
                
                localStorage.setItem('shopci_token', this.token);
                localStorage.setItem('shopci_user', JSON.stringify(this.user));
                
                this.showAlert('Registration successful!', 'success');
                
                setTimeout(() => {
                    window.location.href = '/admin/';
                }, 1000);
            } else {
                this.showAlert(response.message || 'Registration failed', 'danger');
                
                if (response.errors) {
                    Object.values(response.errors).forEach(error => {
                        this.showAlert(error, 'danger');
                    });
                }
            }
        } catch (error) {
            console.error('Registration error:', error);
            this.showAlert('Registration failed. Please try again.', 'danger');
        } finally {
            this.hideLoading(form);
        }
    }

    logout() {
        localStorage.removeItem('shopci_token');
        localStorage.removeItem('shopci_user');
        this.token = null;
        this.user = null;
        window.location.href = '/admin/login';
    }

    updateUserInterface() {
        if (this.user) {
            $('.user-name').text(`${this.user.first_name} ${this.user.last_name}`);
            $('.user-email').text(this.user.email);
            $('.user-role').text(this.user.role);
        }
    }

    async handleAjaxForm(form) {
        const $form = $(form);
        const url = $form.attr('action') || $form.data('url');
        const method = $form.attr('method') || 'POST';
        
        const formData = new FormData(form);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        this.showLoading(form);

        try {
            const response = await $.ajax({
                url: url,
                method: method,
                data: JSON.stringify(data),
                contentType: 'application/json'
            });

            if (response.success) {
                this.showAlert(response.message || 'Operation successful!', 'success');
                
                // Trigger custom event
                $form.trigger('ajax:success', [response]);
                
                // Reset form if specified
                if ($form.data('reset-on-success')) {
                    form.reset();
                }
            } else {
                this.showAlert(response.message || 'Operation failed', 'danger');
                
                if (response.errors) {
                    Object.values(response.errors).forEach(error => {
                        this.showAlert(error, 'danger');
                    });
                }
            }
        } catch (error) {
            console.error('Form submission error:', error);
            this.showAlert('Operation failed. Please try again.', 'danger');
        } finally {
            this.hideLoading(form);
        }
    }

    showLoading(element) {
        const $element = $(element);
        const $submitBtn = $element.find('[type="submit"]');
        
        $submitBtn.prop('disabled', true);
        $submitBtn.html('<span class="spinner"></span> Loading...');
    }

    hideLoading(element) {
        const $element = $(element);
        const $submitBtn = $element.find('[type="submit"]');
        
        $submitBtn.prop('disabled', false);
        $submitBtn.html($submitBtn.data('original-text') || 'Submit');
    }

    showAlert(message, type = 'info') {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        $('.alerts-container').prepend(alertHtml);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            $('.alert').first().fadeOut(() => {
                $(this).remove();
            });
        }, 5000);
    }

    // Utility methods
    formatCurrency(amount, currency = 'USD') {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency
        }).format(amount);
    }

    formatDate(date) {
        return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }).format(new Date(date));
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize the admin panel when document is ready
$(document).ready(() => {
    window.shopciAdmin = new ShopCIAdmin();
});
