-- ShopCI Platform Database Schema
-- This is the main SaaS database that manages all tenants/shops

CREATE DATABASE IF NOT EXISTS shopci_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE shopci_platform;

-- Platform Users Table (SaaS level users)
CREATE TABLE platform_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'merchant') NOT NULL DEFAULT 'merchant',
    status ENUM('active', 'inactive', 'suspended') NOT NULL DEFAULT 'active',
    email_verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status)
);

-- Shops/Tenants Table
CREATE TABLE shops (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL,
    owner_id INT NOT NULL,
    shop_name VARCHAR(255) NOT NULL,
    shop_domain VARCHAR(255) UNIQUE NOT NULL,
    database_name VARCHAR(255) UNIQUE NOT NULL,
    plan_id INT NULL,
    status ENUM('active', 'inactive', 'suspended', 'trial') NOT NULL DEFAULT 'trial',
    trial_ends_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES platform_users(id) ON DELETE CASCADE,
    INDEX idx_owner (owner_id),
    INDEX idx_domain (shop_domain),
    INDEX idx_status (status)
);

-- Subscription Plans Table
CREATE TABLE subscription_plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    billing_cycle ENUM('monthly', 'yearly') NOT NULL,
    features JSON,
    max_products INT DEFAULT NULL,
    max_storage_gb INT DEFAULT NULL,
    max_bandwidth_gb INT DEFAULT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Shop Subscriptions Table
CREATE TABLE shop_subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    shop_id INT NOT NULL,
    plan_id INT NOT NULL,
    status ENUM('active', 'cancelled', 'expired', 'past_due') NOT NULL DEFAULT 'active',
    current_period_start TIMESTAMP NOT NULL,
    current_period_end TIMESTAMP NOT NULL,
    stripe_subscription_id VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (shop_id) REFERENCES shops(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id),
    INDEX idx_shop (shop_id),
    INDEX idx_status (status)
);

-- Platform Settings Table
CREATE TABLE platform_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(255) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key (setting_key)
);

-- Platform Activity Logs
CREATE TABLE platform_activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    shop_id INT NULL,
    action VARCHAR(255) NOT NULL,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES platform_users(id) ON DELETE SET NULL,
    FOREIGN KEY (shop_id) REFERENCES shops(id) ON DELETE SET NULL,
    INDEX idx_user (user_id),
    INDEX idx_shop (shop_id),
    INDEX idx_action (action),
    INDEX idx_created (created_at)
);

-- JWT Tokens Table (for token blacklisting)
CREATE TABLE jwt_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    is_blacklisted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES platform_users(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_token (token_hash),
    INDEX idx_expires (expires_at)
);

-- Password Reset Tokens
CREATE TABLE password_reset_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES platform_users(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_token (token),
    INDEX idx_expires (expires_at)
);

-- Email Verification Tokens
CREATE TABLE email_verification_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES platform_users(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_token (token)
);

-- Insert default subscription plans
INSERT INTO subscription_plans (name, description, price, billing_cycle, features, max_products, max_storage_gb, max_bandwidth_gb) VALUES
('Free Trial', '14-day free trial with basic features', 0.00, 'monthly', '{"ai_features": false, "custom_domain": false, "email_support": true}', 10, 1, 10),
('Starter', 'Perfect for small businesses', 29.00, 'monthly', '{"ai_features": true, "custom_domain": true, "email_support": true, "phone_support": false}', 100, 5, 50),
('Professional', 'For growing businesses', 79.00, 'monthly', '{"ai_features": true, "custom_domain": true, "email_support": true, "phone_support": true, "advanced_analytics": true}', 1000, 20, 200),
('Enterprise', 'For large businesses', 299.00, 'monthly', '{"ai_features": true, "custom_domain": true, "email_support": true, "phone_support": true, "advanced_analytics": true, "white_label": true}', -1, 100, 1000);

-- Insert default platform settings
INSERT INTO platform_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('platform_name', 'ShopCI', 'string', 'Platform name', true),
('platform_version', '1.0.0', 'string', 'Platform version', true),
('maintenance_mode', 'false', 'boolean', 'Maintenance mode status', false),
('registration_enabled', 'true', 'boolean', 'User registration enabled', true),
('trial_period_days', '14', 'integer', 'Trial period in days', false),
('max_shops_per_user', '5', 'integer', 'Maximum shops per user', false);
