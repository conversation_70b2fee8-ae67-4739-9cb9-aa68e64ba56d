#!/bin/bash

# ShopCI Setup Script
echo "🚀 Setting up ShopCI SaaS E-Commerce Platform..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if MySQL is running
check_mysql() {
    print_status "Checking MySQL connection..."
    
    if command -v mysql &> /dev/null; then
        if mysql -u root -e "SELECT 1;" &> /dev/null; then
            print_success "MySQL is running and accessible"
            return 0
        else
            print_warning "MySQL is installed but not accessible with root user"
            print_status "Please ensure MySQL is running and root user has access"
            return 1
        fi
    else
        print_error "MySQL is not installed or not in PATH"
        return 1
    fi
}

# Create databases
create_databases() {
    print_status "Creating databases..."
    
    mysql -u root -e "CREATE DATABASE IF NOT EXISTS shopci_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;" 2>/dev/null
    if [ $? -eq 0 ]; then
        print_success "Created shopci_platform database"
    else
        print_error "Failed to create shopci_platform database"
        return 1
    fi
    
    mysql -u root -e "CREATE DATABASE IF NOT EXISTS shopci_platform_test CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;" 2>/dev/null
    if [ $? -eq 0 ]; then
        print_success "Created shopci_platform_test database"
    else
        print_error "Failed to create shopci_platform_test database"
        return 1
    fi
    
    return 0
}

# Setup backend
setup_backend() {
    print_status "Setting up backend..."
    
    cd backend
    
    # Install dependencies
    print_status "Installing PHP dependencies..."
    if composer install --no-dev --optimize-autoloader; then
        print_success "PHP dependencies installed"
    else
        print_error "Failed to install PHP dependencies"
        return 1
    fi
    
    # Generate encryption key
    print_status "Generating encryption key..."
    ENCRYPTION_KEY=$(php -r "echo bin2hex(random_bytes(32));")
    sed -i.bak "s/encryption.key = hex2bin:your-encryption-key-here/encryption.key = hex2bin:$ENCRYPTION_KEY/" .env
    print_success "Encryption key generated"
    
    # Generate JWT secret
    print_status "Generating JWT secret..."
    JWT_SECRET=$(php -r "echo bin2hex(random_bytes(32));")
    sed -i.bak "s/jwt.secret = your-jwt-secret-key-here/jwt.secret = $JWT_SECRET/" .env
    print_success "JWT secret generated"
    
    # Run migrations
    print_status "Running database migrations..."
    if php spark migrate; then
        print_success "Database migrations completed"
    else
        print_error "Database migrations failed"
        return 1
    fi
    
    cd ..
    return 0
}

# Setup AI server
setup_ai_server() {
    print_status "Setting up AI server..."
    
    cd ai-server
    
    # Check if Python is available
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        print_error "Python is not installed or not in PATH"
        return 1
    fi
    
    # Create virtual environment
    print_status "Creating Python virtual environment..."
    if $PYTHON_CMD -m venv venv; then
        print_success "Virtual environment created"
    else
        print_error "Failed to create virtual environment"
        return 1
    fi
    
    # Activate virtual environment and install dependencies
    print_status "Installing Python dependencies..."
    source venv/bin/activate
    if pip install -r requirements.txt; then
        print_success "Python dependencies installed"
    else
        print_error "Failed to install Python dependencies"
        return 1
    fi
    
    cd ..
    return 0
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p backend/writable/uploads
    mkdir -p backend/writable/cache
    mkdir -p backend/writable/logs
    mkdir -p backend/writable/session
    
    # Set permissions
    chmod -R 755 backend/writable
    
    print_success "Directories created and permissions set"
}

# Main setup process
main() {
    print_status "Starting ShopCI setup process..."
    echo ""
    
    # Check MySQL
    if ! check_mysql; then
        print_error "MySQL setup failed. Please install and configure MySQL first."
        exit 1
    fi
    
    # Create databases
    if ! create_databases; then
        print_error "Database creation failed."
        exit 1
    fi
    
    # Create directories
    create_directories
    
    # Setup backend
    if ! setup_backend; then
        print_error "Backend setup failed."
        exit 1
    fi
    
    # Setup AI server
    if ! setup_ai_server; then
        print_warning "AI server setup failed, but you can continue without it."
    fi
    
    echo ""
    print_success "🎉 ShopCI setup completed successfully!"
    echo ""
    print_status "Next steps:"
    echo "1. Start the backend server: cd backend && php spark serve"
    echo "2. Start the AI server: cd ai-server && source venv/bin/activate && python main.py"
    echo "3. Open your browser and go to: http://localhost:8080/admin/login"
    echo ""
    print_status "Default admin credentials will be created on first run."
}

# Run main function
main
